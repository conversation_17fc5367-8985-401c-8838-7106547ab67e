using UnityEngine;

namespace SkillSystem.Core
{
    #region 空闲状态
    
    /// <summary>
    /// 空闲状态 - 怪物没有目标时的默认状态
    /// </summary>
    public class IdleState : BaseMonsterAIState
    {
        private float idleTime = 0f;
        private const float MAX_IDLE_TIME = 3f; // 最大空闲时间
        
        public override void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            base.EnterState(fromState);
            idleTime = 0f;
            
            // 停止移动
            // aiComponent会处理具体的停止逻辑
        }
        
        public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
        {
            UpdateStateTime(deltaTime);
            idleTime += deltaTime;
            
            // 检查是否找到目标
            if (HasTarget())
            {
                return MonsterAIStateMachine.AIState.Chasing;
            }
            
            // 空闲时间过长，进入巡逻状态
            if (idleTime >= MAX_IDLE_TIME)
            {
                return MonsterAIStateMachine.AIState.Patrol;
            }
            
            return MonsterAIStateMachine.AIState.Idle;
        }
    }
    
    #endregion
    
    #region 巡逻状态
    
    /// <summary>
    /// 巡逻状态 - 怪物在没有目标时的巡逻行为
    /// </summary>
    public class PatrolState : BaseMonsterAIState
    {
        private Vector3 patrolCenter;
        private Vector3 currentPatrolTarget;
        private float patrolRadius = 5f;
        private float patrolWaitTime = 2f;
        private float currentWaitTime = 0f;
        private bool isWaiting = false;
        
        public override void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            base.EnterState(fromState);
            
            // 设置巡逻中心点为当前位置
            patrolCenter = aiComponent.owner.Position;
            
            // 选择第一个巡逻目标
            SelectNewPatrolTarget();
        }
        
        public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
        {
            UpdateStateTime(deltaTime);
            
            // 检查是否找到目标
            if (HasTarget())
            {
                return MonsterAIStateMachine.AIState.Chasing;
            }
            
            // 巡逻逻辑
            if (isWaiting)
            {
                currentWaitTime += deltaTime;
                if (currentWaitTime >= patrolWaitTime)
                {
                    isWaiting = false;
                    SelectNewPatrolTarget();
                }
            }
            else
            {
                // 检查是否到达巡逻点
                float distanceToTarget = Vector3.Distance(aiComponent.owner.Position, currentPatrolTarget);
                if (distanceToTarget <= 1f)
                {
                    // 到达巡逻点，开始等待
                    isWaiting = true;
                    currentWaitTime = 0f;
                }
            }
            
            return MonsterAIStateMachine.AIState.Patrol;
        }
        
        private void SelectNewPatrolTarget()
        {
            // 在巡逻半径内随机选择一个点
            Vector2 randomCircle = Random.insideUnitCircle * patrolRadius;
            currentPatrolTarget = patrolCenter + new Vector3(randomCircle.x, 0, randomCircle.y);
            
            // 确保目标点是可行走的
            currentPatrolTarget = PathfindingHelper.GetNearestWalkablePosition(currentPatrolTarget);
        }
    }
    
    #endregion
    
    #region 搜索状态
    
    /// <summary>
    /// 搜索状态 - 怪物失去目标后的搜索行为
    /// </summary>
    public class SearchingState : BaseMonsterAIState
    {
        private Vector3 lastKnownTargetPosition;
        private float searchTime = 0f;
        private const float MAX_SEARCH_TIME = 5f;
        
        public override void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            base.EnterState(fromState);
            searchTime = 0f;
            
            // 记录目标最后已知位置
            var target = aiComponent?.GetCurrentTarget();
            if (target != null)
            {
                lastKnownTargetPosition = target.Position;
            }
            else
            {
                lastKnownTargetPosition = aiComponent.owner.Position;
            }
        }
        
        public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
        {
            UpdateStateTime(deltaTime);
            searchTime += deltaTime;
            
            // 检查是否重新找到目标
            if (HasTarget())
            {
                return MonsterAIStateMachine.AIState.Chasing;
            }
            
            // 搜索时间过长，返回空闲状态
            if (searchTime >= MAX_SEARCH_TIME)
            {
                return MonsterAIStateMachine.AIState.Idle;
            }
            
            // 在最后已知位置附近搜索
            // 这里可以实现更复杂的搜索逻辑
            
            return MonsterAIStateMachine.AIState.Searching;
        }
    }
    
    #endregion
    
    #region 追击状态
    
    /// <summary>
    /// 追击状态 - 怪物追击目标的行为
    /// </summary>
    public class ChasingState : BaseMonsterAIState
    {
        public override void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            base.EnterState(fromState);
        }
        
        public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
        {
            UpdateStateTime(deltaTime);
            
            // 检查目标是否还存在
            if (!HasTarget())
            {
                return MonsterAIStateMachine.AIState.Searching;
            }
            
            // 检查是否进入攻击范围
            if (IsTargetInAttackRange())
            {
                return MonsterAIStateMachine.AIState.Attacking;
            }
            
            // 检查是否超出追击范围
            if (!IsTargetInChaseRange())
            {
                return MonsterAIStateMachine.AIState.Searching;
            }
            
            // 继续追击
            return MonsterAIStateMachine.AIState.Chasing;
        }
    }
    
    #endregion
    
    #region 攻击状态
    
    /// <summary>
    /// 攻击状态 - 怪物攻击目标的行为
    /// </summary>
    public class AttackingState : BaseMonsterAIState
    {
        private float lastAttackTime = 0f;
        
        public override void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            base.EnterState(fromState);
            lastAttackTime = Time.time;
        }
        
        public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
        {
            UpdateStateTime(deltaTime);
            
            // 检查目标是否还存在
            if (!HasTarget())
            {
                return MonsterAIStateMachine.AIState.Searching;
            }
            
            // 检查是否离开攻击范围
            if (!IsTargetInAttackRange())
            {
                // 添加一些缓冲区避免频繁切换状态
                float bufferDistance = aiComponent.attackRange * 1.2f;
                var target = aiComponent.GetCurrentTarget();
                float distance = Vector3.Distance(aiComponent.owner.Position, target.Position);
                
                if (distance > bufferDistance)
                {
                    return MonsterAIStateMachine.AIState.Chasing;
                }
            }
            
            // 检查是否超出追击范围
            if (!IsTargetInChaseRange())
            {
                return MonsterAIStateMachine.AIState.Searching;
            }
            
            // 继续攻击
            return MonsterAIStateMachine.AIState.Attacking;
        }
    }
    
    #endregion
    
    #region 返回状态
    
    /// <summary>
    /// 返回状态 - 怪物返回出生点的行为
    /// </summary>
    public class ReturningState : BaseMonsterAIState
    {
        private Vector3 spawnPosition;
        
        public override void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            base.EnterState(fromState);
            
            // 这里应该从怪物配置中获取出生点
            // 暂时使用当前位置作为出生点
            spawnPosition = aiComponent.owner.Position;
        }
        
        public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
        {
            UpdateStateTime(deltaTime);
            
            // 检查是否到达出生点
            float distanceToSpawn = Vector3.Distance(aiComponent.owner.Position, spawnPosition);
            if (distanceToSpawn <= 2f)
            {
                return MonsterAIStateMachine.AIState.Idle;
            }
            
            // 继续返回
            return MonsterAIStateMachine.AIState.Returning;
        }
    }
    
    #endregion
    
    #region 眩晕状态
    
    /// <summary>
    /// 眩晕状态 - 怪物被眩晕时的行为
    /// </summary>
    public class StunnedState : BaseMonsterAIState
    {
        private float stunDuration = 2f;
        
        public override void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            base.EnterState(fromState);
            
            // 停止所有行动
        }
        
        public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
        {
            UpdateStateTime(deltaTime);
            
            // 检查眩晕时间是否结束
            if (stateTime >= stunDuration)
            {
                // 根据之前的状态决定下一个状态
                if (HasTarget())
                {
                    return IsTargetInAttackRange() ? 
                        MonsterAIStateMachine.AIState.Attacking : 
                        MonsterAIStateMachine.AIState.Chasing;
                }
                else
                {
                    return MonsterAIStateMachine.AIState.Idle;
                }
            }
            
            return MonsterAIStateMachine.AIState.Stunned;
        }
        
        public override bool CanTransitionTo(MonsterAIStateMachine.AIState targetState)
        {
            // 眩晕状态只能转换到死亡状态
            return targetState == MonsterAIStateMachine.AIState.Dead;
        }
        
        /// <summary>
        /// 设置眩晕持续时间
        /// </summary>
        public void SetStunDuration(float duration)
        {
            stunDuration = duration;
        }
    }
    
    #endregion
    
    #region 死亡状态
    
    /// <summary>
    /// 死亡状态 - 怪物死亡时的行为
    /// </summary>
    public class DeadState : BaseMonsterAIState
    {
        public override void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            base.EnterState(fromState);
            
            // 停止所有行动
            // 播放死亡动画等
        }
        
        public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
        {
            // 死亡状态不会转换到其他状态
            return MonsterAIStateMachine.AIState.Dead;
        }
        
        public override bool CanTransitionTo(MonsterAIStateMachine.AIState targetState)
        {
            // 死亡状态不能转换到任何其他状态
            return false;
        }
    }
    
    #endregion
}
