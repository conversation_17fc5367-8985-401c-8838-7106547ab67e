using System.Collections.Generic;
using UnityEngine;
using SkillSystem.Core;
using Cysharp.Threading.Tasks;

/// <summary>
/// 怪物管理器 - 统一管理所有怪物AI的更新和协调
/// 继承自BaseManager，集成到框架的管理器系统中
/// </summary>
public class MonsterManager : BaseManager
{
    #region 单例模式
    
    private static MonsterManager _instance;
    public static MonsterManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new MonsterManager();
            }
            return _instance;
        }
    }
    
    #endregion
    
    #region 配置参数
    
    [Header("管理器设置")]
    public bool enableGlobalAI = true;              // 全局AI开关
    public float globalUpdateInterval = 0.1f;       // 全局更新间隔
    public int maxMonstersPerFrame = 10;            // 每帧最大更新怪物数量
    public bool enablePerformanceOptimization = true; // 启用性能优化
    
    [Header("调试设置")]
    public bool enableDebugLog = false;             // 启用调试日志
    public bool showAIStatus = false;               // 显示AI状态
    
    #endregion
    
    #region 内部状态
    
    private List<MonsterEntity> registeredMonsters = new List<MonsterEntity>();
    private Queue<MonsterEntity> updateQueue = new Queue<MonsterEntity>();
    private float lastGlobalUpdateTime;
    private int currentUpdateIndex = 0;
    
    // 性能统计
    private int totalMonstersCount = 0;
    private int activeMonstersCount = 0;
    private float averageUpdateTime = 0f;
    
    #endregion
    
    #region 初始化和销毁
    
    /// <summary>
    /// 初始化管理器
    /// </summary>
    public override void Initialize()
    {
        base.Initialize();
        
        registeredMonsters.Clear();
        updateQueue.Clear();
        lastGlobalUpdateTime = Time.time;
        currentUpdateIndex = 0;
        
        Debug.Log("[MonsterManager] 怪物管理器初始化完成");
    }
    
    /// <summary>
    /// 销毁管理器
    /// </summary>
    public override void Dispose()
    {
        // 清理所有注册的怪物
        foreach (var monster in registeredMonsters)
        {
            if (monster != null)
            {
                monster.SetAIEnabled(false);
            }
        }
        
        registeredMonsters.Clear();
        updateQueue.Clear();
        
        base.Dispose();
        Debug.Log("[MonsterManager] 怪物管理器已销毁");
    }
    
    #endregion
    
    #region 怪物注册管理
    
    /// <summary>
    /// 注册怪物到管理器
    /// </summary>
    public void RegisterMonster(MonsterEntity monster)
    {
        if (monster == null)
        {
            Debug.LogWarning("[MonsterManager] 尝试注册空的怪物实体");
            return;
        }
        
        if (registeredMonsters.Contains(monster))
        {
            Debug.LogWarning($"[MonsterManager] 怪物 {monster.UniqueId} 已经注册过");
            return;
        }
        
        registeredMonsters.Add(monster);
        totalMonstersCount = registeredMonsters.Count;
        
        // 设置怪物AI状态
        monster.SetAIEnabled(enableGlobalAI);
        
        if (enableDebugLog)
        {
            Debug.Log($"[MonsterManager] 注册怪物: {monster.UniqueId}, 总数: {totalMonstersCount}");
        }
    }
    
    /// <summary>
    /// 注销怪物
    /// </summary>
    public void UnregisterMonster(MonsterEntity monster)
    {
        if (monster == null) return;
        
        if (registeredMonsters.Remove(monster))
        {
            totalMonstersCount = registeredMonsters.Count;
            
            if (enableDebugLog)
            {
                Debug.Log($"[MonsterManager] 注销怪物: {monster.UniqueId}, 剩余: {totalMonstersCount}");
            }
        }
    }
    
    /// <summary>
    /// 获取所有注册的怪物
    /// </summary>
    public List<MonsterEntity> GetAllMonsters()
    {
        return new List<MonsterEntity>(registeredMonsters);
    }
    
    /// <summary>
    /// 获取活跃的怪物数量
    /// </summary>
    public int GetActiveMonsterCount()
    {
        return activeMonstersCount;
    }
    
    /// <summary>
    /// 获取总怪物数量
    /// </summary>
    public int GetTotalMonsterCount()
    {
        return totalMonstersCount;
    }
    
    #endregion
    
    #region 更新逻辑
    
    /// <summary>
    /// 主更新函数
    /// </summary>
    public override void Update(float deltaTime)
    {
        if (!enableGlobalAI || registeredMonsters.Count == 0) return;
        
        // 检查全局更新间隔
        if (Time.time - lastGlobalUpdateTime < globalUpdateInterval) return;
        lastGlobalUpdateTime = Time.time;
        
        // 性能优化：分帧更新
        if (enablePerformanceOptimization)
        {
            UpdateMonstersWithOptimization(deltaTime);
        }
        else
        {
            UpdateAllMonsters(deltaTime);
        }
        
        // 清理无效怪物
        CleanupInvalidMonsters();
        
        // 更新统计信息
        UpdateStatistics();
    }
    
    /// <summary>
    /// 优化的分帧更新
    /// </summary>
    private void UpdateMonstersWithOptimization(float deltaTime)
    {
        int updatedCount = 0;
        int startIndex = currentUpdateIndex;
        
        while (updatedCount < maxMonstersPerFrame && registeredMonsters.Count > 0)
        {
            if (currentUpdateIndex >= registeredMonsters.Count)
            {
                currentUpdateIndex = 0;
            }
            
            var monster = registeredMonsters[currentUpdateIndex];
            if (monster != null && monster.IsAlive)
            {
                UpdateSingleMonster(monster, deltaTime);
                updatedCount++;
            }
            
            currentUpdateIndex++;
            
            // 防止无限循环
            if (currentUpdateIndex == startIndex && updatedCount == 0)
            {
                break;
            }
        }
    }
    
    /// <summary>
    /// 更新所有怪物
    /// </summary>
    private void UpdateAllMonsters(float deltaTime)
    {
        foreach (var monster in registeredMonsters)
        {
            if (monster != null && monster.IsAlive)
            {
                UpdateSingleMonster(monster, deltaTime);
            }
        }
    }
    
    /// <summary>
    /// 更新单个怪物
    /// </summary>
    private void UpdateSingleMonster(MonsterEntity monster, float deltaTime)
    {
        try
        {
            // 怪物的AI更新已经在MonsterEntity.Update中处理
            // 这里可以添加额外的管理器级别的逻辑
            
            if (showAIStatus && enableDebugLog)
            {
                Debug.Log(monster.GetAIStatus());
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[MonsterManager] 更新怪物 {monster.UniqueId} 时发生异常: {e.Message}");
        }
    }
    
    /// <summary>
    /// 清理无效怪物
    /// </summary>
    private void CleanupInvalidMonsters()
    {
        registeredMonsters.RemoveAll(monster => monster == null || !monster.IsAlive);
    }
    
    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics()
    {
        activeMonstersCount = 0;
        foreach (var monster in registeredMonsters)
        {
            if (monster != null && monster.IsAlive)
            {
                activeMonstersCount++;
            }
        }
    }
    
    #endregion
    
    #region 全局控制接口
    
    /// <summary>
    /// 设置全局AI开关
    /// </summary>
    public void SetGlobalAIEnabled(bool enabled)
    {
        enableGlobalAI = enabled;
        
        foreach (var monster in registeredMonsters)
        {
            if (monster != null)
            {
                monster.SetAIEnabled(enabled);
            }
        }
        
        Debug.Log($"[MonsterManager] 全局AI状态设置为: {enabled}");
    }
    
    /// <summary>
    /// 暂停所有怪物AI
    /// </summary>
    public void PauseAllMonsterAI()
    {
        SetGlobalAIEnabled(false);
    }
    
    /// <summary>
    /// 恢复所有怪物AI
    /// </summary>
    public void ResumeAllMonsterAI()
    {
        SetGlobalAIEnabled(true);
    }
    
    /// <summary>
    /// 让所有怪物攻击指定目标
    /// </summary>
    public void SetAllMonstersTarget(Entity target)
    {
        if (target == null) return;
        
        foreach (var monster in registeredMonsters)
        {
            if (monster != null && monster.IsAlive)
            {
                monster.SetAITarget(target);
            }
        }
        
        Debug.Log($"[MonsterManager] 设置所有怪物目标为: {target.UniqueId}");
    }
    
    #endregion

    #region 怪物创建和管理

    /// <summary>
    /// 创建怪物实体
    /// </summary>
    public async UniTask<MonsterEntity> CreateMonster(int entityCfgId, Vector3 position, Quaternion rotation = default)
    {
        try
        {
            var monster = new MonsterEntity();
            await monster.Initialize(entityCfgId);

            // 设置位置和旋转
            if (monster.entityObject != null)
            {
                monster.entityObject.transform.position = position;
                if (rotation != default)
                {
                    monster.entityObject.transform.rotation = rotation;
                }
            }

            // 注册到管理器
            RegisterMonster(monster);

            Debug.Log($"[MonsterManager] 创建怪物成功: {monster.UniqueId} 位置: {position}");
            return monster;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[MonsterManager] 创建怪物失败: {e.Message}");
            return null;
        }
    }

    /// <summary>
    /// 批量创建怪物
    /// </summary>
    public async UniTask<List<MonsterEntity>> CreateMonsters(int entityCfgId, Vector3[] positions, Quaternion[] rotations = null)
    {
        var monsters = new List<MonsterEntity>();

        for (int i = 0; i < positions.Length; i++)
        {
            var rotation = rotations != null && i < rotations.Length ? rotations[i] : Quaternion.identity;
            var monster = await CreateMonster(entityCfgId, positions[i], rotation);

            if (monster != null)
            {
                monsters.Add(monster);
            }
        }

        Debug.Log($"[MonsterManager] 批量创建怪物完成: {monsters.Count}/{positions.Length}");
        return monsters;
    }

    /// <summary>
    /// 销毁怪物
    /// </summary>
    public void DestroyMonster(MonsterEntity monster)
    {
        if (monster == null) return;

        // 从管理器注销
        UnregisterMonster(monster);

        // 禁用AI
        monster.SetAIEnabled(false);

        // 销毁GameObject
        if (monster.entityObject != null)
        {
            Object.Destroy(monster.entityObject);
        }

        Debug.Log($"[MonsterManager] 销毁怪物: {monster.UniqueId}");
    }

    /// <summary>
    /// 销毁所有怪物
    /// </summary>
    public void DestroyAllMonsters()
    {
        var monstersToDestroy = new List<MonsterEntity>(registeredMonsters);

        foreach (var monster in monstersToDestroy)
        {
            DestroyMonster(monster);
        }

        Debug.Log("[MonsterManager] 销毁所有怪物完成");
    }

    #endregion

    #region 查询和统计

    /// <summary>
    /// 根据距离查找最近的怪物
    /// </summary>
    public MonsterEntity FindNearestMonster(Vector3 position, float maxDistance = float.MaxValue)
    {
        MonsterEntity nearestMonster = null;
        float nearestDistance = maxDistance;

        foreach (var monster in registeredMonsters)
        {
            if (monster == null || !monster.IsAlive) continue;

            float distance = Vector3.Distance(position, monster.Position);
            if (distance < nearestDistance)
            {
                nearestDistance = distance;
                nearestMonster = monster;
            }
        }

        return nearestMonster;
    }

    /// <summary>
    /// 查找指定范围内的怪物
    /// </summary>
    public List<MonsterEntity> FindMonstersInRange(Vector3 center, float range)
    {
        var monstersInRange = new List<MonsterEntity>();

        foreach (var monster in registeredMonsters)
        {
            if (monster == null || !monster.IsAlive) continue;

            float distance = Vector3.Distance(center, monster.Position);
            if (distance <= range)
            {
                monstersInRange.Add(monster);
            }
        }

        return monstersInRange;
    }

    /// <summary>
    /// 获取管理器状态信息
    /// </summary>
    public string GetManagerStatus()
    {
        return $"[MonsterManager] 总数:{totalMonstersCount}, 活跃:{activeMonstersCount}, " +
               $"全局AI:{enableGlobalAI}, 更新间隔:{globalUpdateInterval}s";
    }

    /// <summary>
    /// 获取详细统计信息
    /// </summary>
    public MonsterStatistics GetStatistics()
    {
        var stats = new MonsterStatistics
        {
            TotalCount = totalMonstersCount,
            ActiveCount = activeMonstersCount,
            DeadCount = totalMonstersCount - activeMonstersCount,
            AverageUpdateTime = averageUpdateTime,
            GlobalAIEnabled = enableGlobalAI
        };

        // 按状态统计
        foreach (var monster in registeredMonsters)
        {
            if (monster?.GetAIComponent() != null)
            {
                var state = monster.GetAIComponent().GetCurrentState();
                switch (state)
                {
                    case MonsterAIComponent.MonsterAIState.Idle:
                        stats.IdleCount++;
                        break;
                    case MonsterAIComponent.MonsterAIState.Chasing:
                        stats.ChasingCount++;
                        break;
                    case MonsterAIComponent.MonsterAIState.Attacking:
                        stats.AttackingCount++;
                        break;
                }
            }
        }

        return stats;
    }

    #endregion

    #region 调试和工具

    /// <summary>
    /// 打印所有怪物状态
    /// </summary>
    public void PrintAllMonsterStatus()
    {
        Debug.Log($"[MonsterManager] ===== 怪物状态报告 =====");
        Debug.Log(GetManagerStatus());

        foreach (var monster in registeredMonsters)
        {
            if (monster != null)
            {
                Debug.Log(monster.GetAIStatus());
            }
        }

        Debug.Log($"[MonsterManager] ===== 报告结束 =====");
    }

    /// <summary>
    /// 设置调试模式
    /// </summary>
    public void SetDebugMode(bool enableLog, bool showStatus)
    {
        enableDebugLog = enableLog;
        showAIStatus = showStatus;

        Debug.Log($"[MonsterManager] 调试模式设置: 日志={enableLog}, 状态显示={showStatus}");
    }

    #endregion

    #region 数据结构

    /// <summary>
    /// 怪物统计信息
    /// </summary>
    public struct MonsterStatistics
    {
        public int TotalCount;
        public int ActiveCount;
        public int DeadCount;
        public int IdleCount;
        public int ChasingCount;
        public int AttackingCount;
        public float AverageUpdateTime;
        public bool GlobalAIEnabled;

        public override string ToString()
        {
            return $"总数:{TotalCount}, 活跃:{ActiveCount}, 死亡:{DeadCount}, " +
                   $"空闲:{IdleCount}, 追击:{ChasingCount}, 攻击:{AttackingCount}, " +
                   $"平均更新时间:{AverageUpdateTime:F3}ms, 全局AI:{GlobalAIEnabled}";
        }
    }

    #endregion
}
