# 🤖 怪物AI系统使用指南

## 📋 系统概述

新的怪物AI系统采用模块化设计，不依赖Unity的MonoBehaviour，完全集成到项目的Entity框架中。系统包含自动锁敌、智能移动、技能释放等核心功能。

## 🏗️ 系统架构

### 核心组件

1. **MonsterAIComponent** - AI核心组件
   - 自动目标检测和锁定
   - 智能移动和寻路
   - 状态机管理
   - 技能释放控制

2. **MonsterAIStateMachine** - AI状态机
   - 空闲、巡逻、搜索、追击、攻击等状态
   - 状态转换逻辑
   - 状态行为管理

3. **MonsterEntity** - 怪物实体
   - 继承自Entity基类
   - 集成AI组件
   - 自动注册到管理器

4. **MonsterManager** - 怪物管理器
   - 统一管理所有怪物AI
   - 性能优化和分帧更新
   - 全局控制接口

5. **PathfindingHelper** - 寻路辅助
   - A*寻路系统集成
   - 寻路配置和管理
   - 移动控制

## 🚀 快速开始

### 1. 创建怪物

```csharp
// 通过MonsterManager创建怪物
var monster = await MonsterManager.Instance.CreateMonster(
    entityCfgId: 2,           // 怪物配置ID
    position: Vector3.zero,   // 生成位置
    rotation: Quaternion.identity // 生成旋转
);
```

### 2. 配置AI参数

```csharp
var aiComponent = monster.GetAIComponent();
if (aiComponent != null)
{
    aiComponent.detectionRange = 15f;    // 检测范围
    aiComponent.attackRange = 3f;        // 攻击范围
    aiComponent.chaseRange = 20f;        // 追击范围
    aiComponent.moveSpeed = 3f;          // 移动速度
    aiComponent.skillReleaseInterval = 2f; // 技能释放间隔
    aiComponent.enableAutoSkill = true;   // 启用自动技能
    aiComponent.usePathfinding = true;    // 启用寻路
}
```

### 3. 控制AI状态

```csharp
// 启用/禁用AI
monster.SetAIEnabled(true);

// 强制设置目标
monster.SetAITarget(heroEntity);

// 设置眩晕状态
aiComponent.SetStunned(3f); // 眩晕3秒
```

## 🎯 AI状态说明

### 状态类型

1. **Idle（空闲）** - 默认状态，搜索目标
2. **Patrol（巡逻）** - 在区域内巡逻
3. **Searching（搜索）** - 失去目标后搜索
4. **Chasing（追击）** - 追击目标
5. **Attacking（攻击）** - 攻击目标
6. **Returning（返回）** - 返回出生点
7. **Stunned（眩晕）** - 被控制状态
8. **Dead（死亡）** - 死亡状态

### 状态转换

```
Idle → Patrol → Searching → Chasing → Attacking
  ↑                                      ↓
  ←←←←←←←←←← Returning ←←←←←←←←←←←←←←←←←←←
```

## 🔧 高级配置

### 1. 自定义AI行为

```csharp
// 创建自定义状态
public class CustomAttackState : BaseMonsterAIState
{
    public override MonsterAIStateMachine.AIState UpdateState(float deltaTime)
    {
        // 自定义攻击逻辑
        return MonsterAIStateMachine.AIState.Attacking;
    }
}
```

### 2. 批量管理

```csharp
// 批量创建怪物
Vector3[] positions = { Vector3.zero, Vector3.forward * 5, Vector3.back * 5 };
var monsters = await MonsterManager.Instance.CreateMonsters(2, positions);

// 全局控制
MonsterManager.Instance.SetGlobalAIEnabled(false); // 暂停所有AI
MonsterManager.Instance.SetAllMonstersTarget(hero); // 设置统一目标
```

### 3. 性能优化

```csharp
// 配置性能参数
MonsterManager.Instance.enablePerformanceOptimization = true;
MonsterManager.Instance.maxMonstersPerFrame = 10;
MonsterManager.Instance.globalUpdateInterval = 0.1f;
```

## 📊 调试和监控

### 1. 状态监控

```csharp
// 获取AI状态
string status = monster.GetAIStatus();
var currentState = aiComponent.GetCurrentState();

// 管理器统计
var stats = MonsterManager.Instance.GetStatistics();
Debug.Log($"活跃怪物: {stats.ActiveCount}, 攻击中: {stats.AttackingCount}");
```

### 2. 调试工具

```csharp
// 启用调试模式
MonsterManager.Instance.SetDebugMode(enableLog: true, showStatus: true);

// 打印所有状态
MonsterManager.Instance.PrintAllMonsterStatus();
```

### 3. 测试脚本

使用 `MonsterAITest` 组件进行系统测试：

- 自动生成测试怪物
- 实时状态显示
- 交互式控制面板
- 性能监控

## 🎮 控制键位（测试模式）

- **P** - 暂停/恢复所有AI
- **R** - 重新开始测试
- **S** - 打印状态信息
- **T** - 设置所有怪物攻击英雄

## ⚙️ 配置参数说明

### AI基础设置
- `enableAI` - 是否启用AI
- `updateInterval` - AI更新间隔
- `detectionRange` - 检测范围
- `attackRange` - 攻击范围
- `chaseRange` - 追击范围
- `loseTargetTime` - 失去目标等待时间

### 移动设置
- `moveSpeed` - 移动速度
- `rotationSpeed` - 旋转速度
- `stopDistance` - 停止距离
- `usePathfinding` - 是否使用寻路

### 技能设置
- `enableAutoSkill` - 启用自动技能
- `skillReleaseInterval` - 技能释放间隔
- `prioritizeCloseTargets` - 优先攻击近距离目标

## 🔗 与现有系统集成

### 1. Entity系统
- 继承Entity基类
- 自动注册到EntityManager
- 支持技能系统

### 2. 技能系统
- 自动技能释放
- 目标选择逻辑
- 敌对关系判断

### 3. 寻路系统
- A*寻路集成
- 自动配置FollowerEntity
- 智能移动控制

## 🚨 注意事项

1. **性能考虑**
   - 大量怪物时启用性能优化
   - 合理设置更新间隔
   - 使用分帧更新

2. **寻路系统**
   - 确保场景有寻路网格
   - 检查A*系统是否扫描完成
   - 合理设置代理参数

3. **状态管理**
   - 避免频繁状态切换
   - 合理设置状态转换条件
   - 注意死亡状态处理

## 📝 示例代码

完整的怪物创建和配置示例：

```csharp
public async UniTask CreateAndConfigureMonster()
{
    // 创建怪物
    var monster = await MonsterManager.Instance.CreateMonster(
        entityCfgId: 2,
        position: Vector3.zero
    );
    
    if (monster != null)
    {
        // 配置AI
        var ai = monster.GetAIComponent();
        ai.detectionRange = 15f;
        ai.attackRange = 3f;
        ai.enableAutoSkill = true;
        
        // 设置目标
        var hero = EntityManager.Instance.HeroEntity;
        if (hero != null)
        {
            monster.SetAITarget(hero);
        }
        
        Debug.Log($"怪物创建完成: {monster.UniqueId}");
    }
}
```

这套AI系统提供了完整的怪物智能行为，支持自动锁敌、智能移动、技能释放等功能，完全集成到项目框架中，性能优化且易于扩展。
