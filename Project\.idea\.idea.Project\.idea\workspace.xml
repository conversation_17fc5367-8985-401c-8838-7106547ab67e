<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2d37d096-c114-4621-bbc8-1dbeb6376494" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Assets/Res/Config/model_art_artmodelmodel.bytes" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Res/Config/model_art_artmodelmodel.bytes" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Res/Config/model_art_artuimodel.bytes" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Res/Config/model_art_artuimodel.bytes" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Res/Config/model_viewconfig_viewconfigmodel.bytes" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Res/Config/model_viewconfig_viewconfigmodel.bytes" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Scripts/WaveSystem/MonsterAI.cs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Scripts/WaveSystem/MonsterAI.cs.meta" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/com.arongranberg.astar/Drawing/PackageTools/Editor/PackageToolsEditor.asmdef" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/com.arongranberg.astar/Drawing/PackageTools/Editor/PackageToolsEditor.asmdef" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/com.arongranberg.astar/PackageTools/Editor/PackageToolsEditor.asmdef" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/com.arongranberg.astar/PackageTools/Editor/PackageToolsEditor.asmdef" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/Packages/com.unity.probuilder/Settings.json" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/Packages/com.unity.probuilder/Settings.json" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/575c8d5d94a9444f82eba927ac72f8df1dfe00/56/ab692cf6/Transform.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Editor/SvnMerge/MergeLogInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Editor/SvnMerge/SVNLog.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Editor/SvnMerge/SVNLogInfo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Editor/SvnMerge/SVNManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Editor/ToolsBar/ToolBarMenu.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Editor/UITools/CreateUIView.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Res/GabrielAguiarProductions/Scripts/UniqueProjectiles/ProjectileMoveScript.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Base/ComponentBase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Base/SingletonManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Base/SingletonMod.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Base/UIBase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Define/EnumDefine.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Define/EventDefine.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Gen/Bean/Entity_Entity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Gen/Bean/Skill_Skill.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Gen/Tables.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/CameraManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/EntityManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/EventManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/GameOverManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/GameSceneManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/NetMessageHandleManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/ResManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/SkillManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/TableManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Manager/UIManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Mod/HeroMod.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Mod/WaveSystemMod.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Net/NetClientManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/DamageAreas/AllDamageAreas.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/Entity/Entity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/Entity/HeroEntity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/Entity/MonsterEntity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/Skills/BaseSkill.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/Skills/SkillsDemo.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/Skills/UniversalSkill.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/UniqueIdGenerator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/SkillSystem/Core/Visualization/SkillRangeVisualizer.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/UIView/MianUI/UIMainView.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/UIView/UIBattle/JoyStickComponent.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/UIView/UIRlShop/UIRlShopView.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/Util/UtilHelper.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/WaveSystem/MonsterCreationRequest.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/WaveSystem/MonsterCreationResult.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/WaveSystem/MonsterSpawnManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Scripts/WaveSystem/WaveManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.tuyoogame.yooasset@b6474a7f06e7/Runtime/FileSystem/FileSystemParameters.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.tuyoogame.yooasset@b6474a7f06e7/Runtime/ResourcePackage/Interface/IPlayMode.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.tuyoogame.yooasset@b6474a7f06e7/Runtime/ResourcePackage/Operation/RequestPackageVersionOperation.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.tuyoogame.yooasset@b6474a7f06e7/Runtime/ResourcePackage/ResourcePackage.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2yunlc9U8kqaTWLXMcOYSbw8Jpb" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Attach to Unity Editor.Attach to Unity Editor.executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Attach to Unity Editor.Attach to Unity Editor">
    <configuration name="Standalone Player" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="$PROJECT_DIR$/../Release\Project.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="D:\Git\GameFramework\Release" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\Hub\Editor\6000.0.51f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath D:\Git\GameFramework\Project -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="D:\Git\GameFramework\Project" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\Hub\Editor\6000.0.51f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath D:\Git\GameFramework\Project -testResults Logs/results.xml -logFile Logs/Editor.log -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="D:\Git\GameFramework\Project" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor &amp; Play" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2d37d096-c114-4621-bbc8-1dbeb6376494" name="Changes" comment="" />
      <created>1750693479147</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750693479147</updated>
      <workItem from="1750693480335" duration="550000" />
      <workItem from="1750739204089" duration="1332000" />
      <workItem from="1751020409114" duration="1048000" />
      <workItem from="1751108710034" duration="4455000" />
      <workItem from="1751114892544" duration="844000" />
      <workItem from="1751170527606" duration="7154000" />
      <workItem from="1751186063439" duration="4485000" />
      <workItem from="1751805197530" duration="766000" />
      <workItem from="1751891129951" duration="3779000" />
      <workItem from="1752028505521" duration="4321000" />
      <workItem from="1752143636038" duration="3725000" />
      <workItem from="1752380332419" duration="5804000" />
      <workItem from="1752400245482" duration="709000" />
      <workItem from="1752475048018" duration="9754000" />
      <workItem from="1752551805945" duration="4361000" />
      <workItem from="1752558666327" duration="2841000" />
      <workItem from="1752648044355" duration="8434000" />
      <workItem from="1752729465187" duration="1507000" />
      <workItem from="1752732866673" duration="8118000" />
      <workItem from="1753108158535" duration="1349000" />
      <workItem from="1753407390697" duration="7748000" />
      <workItem from="1753422328281" duration="48000" />
      <workItem from="1753422409150" duration="933000" />
      <workItem from="1753424785888" duration="9427000" />
      <workItem from="1753589738453" duration="3743000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>