using UnityEngine;
using Pathfinding;

namespace SkillSystem.Core
{
    /// <summary>
    /// 寻路辅助类 - 为怪物AI提供寻路功能的配置和管理
    /// </summary>
    public static class PathfindingHelper
    {
        #region 配置常量
        
        // 默认寻路配置
        public const float DEFAULT_MOVE_SPEED = 3f;
        public const float DEFAULT_ROTATION_SPEED = 180f;
        public const float DEFAULT_STOP_DISTANCE = 0.5f;
        public const float DEFAULT_AGENT_RADIUS = 0.5f;
        public const float DEFAULT_AGENT_HEIGHT = 2f;
        
        // 寻路图层设置
        public const int DEFAULT_GRAPH_MASK = -1;  // 所有图层
        public const int DEFAULT_TRAVERSABLE_TAGS = -1;  // 所有标签
        
        #endregion
        
        #region 寻路组件配置
        
        /// <summary>
        /// 为GameObject配置FollowerEntity组件
        /// </summary>
        public static FollowerEntity ConfigureFollowerEntity(GameObject gameObject, MonsterAIComponent aiComponent)
        {
            if (gameObject == null || aiComponent == null) return null;
            
            // 获取或添加FollowerEntity组件
            var followerEntity = gameObject.GetComponent<FollowerEntity>();
            if (followerEntity == null)
            {
                followerEntity = gameObject.AddComponent<FollowerEntity>();
            }
            
            // 配置基础移动参数
            ConfigureMovementSettings(followerEntity, aiComponent);
            
            // 配置寻路设置
            ConfigurePathfindingSettings(followerEntity);
            
            // 配置代理形状
            ConfigureAgentShape(followerEntity);
            
            Debug.Log($"[PathfindingHelper] 为 {gameObject.name} 配置FollowerEntity完成");
            return followerEntity;
        }
        
        /// <summary>
        /// 配置移动设置
        /// </summary>
        private static void ConfigureMovementSettings(FollowerEntity followerEntity, MonsterAIComponent aiComponent)
        {
            // 设置移动速度
            followerEntity.maxSpeed = aiComponent.moveSpeed;
            
            // 设置旋转速度
            followerEntity.rotationSpeed = aiComponent.rotationSpeed;
            
            // 设置停止距离
            followerEntity.endReachedDistance = aiComponent.stopDistance;
            
            // 启用重力（如果需要）
            followerEntity.enableGravity = true;
            
            // 设置地面检测层
            followerEntity.groundMask = LayerMask.GetMask("Default", "Ground");
            
            Debug.Log($"[PathfindingHelper] 移动设置配置完成 - 速度:{aiComponent.moveSpeed}, 旋转:{aiComponent.rotationSpeed}");
        }
        
        /// <summary>
        /// 配置寻路设置
        /// </summary>
        private static void ConfigurePathfindingSettings(FollowerEntity followerEntity)
        {
            // 获取寻路设置引用
            ref var pathfindingSettings = ref followerEntity.pathfindingSettings;
            
            // 设置图层掩码（使用所有可用图层）
            pathfindingSettings.graphMask = DEFAULT_GRAPH_MASK;
            
            // 设置可遍历标签（使用所有标签）
            pathfindingSettings.traversableTags = DEFAULT_TRAVERSABLE_TAGS;
            
            // 设置自动重新计算路径
            followerEntity.autoRepath.mode = AutoRepathPolicy.Mode.Dynamic;
            followerEntity.autoRepath.period = 2f; // 每2秒重新计算一次路径
            
            Debug.Log("[PathfindingHelper] 寻路设置配置完成");
        }
        
        /// <summary>
        /// 配置代理形状
        /// </summary>
        private static void ConfigureAgentShape(FollowerEntity followerEntity)
        {
            // 设置代理半径和高度
            followerEntity.radius = DEFAULT_AGENT_RADIUS;
            followerEntity.height = DEFAULT_AGENT_HEIGHT;
            
            Debug.Log($"[PathfindingHelper] 代理形状配置完成 - 半径:{DEFAULT_AGENT_RADIUS}, 高度:{DEFAULT_AGENT_HEIGHT}");
        }
        
        #endregion
        
        #region 寻路状态检查
        
        /// <summary>
        /// 检查寻路代理是否有效
        /// </summary>
        public static bool IsPathfindingAgentValid(IAstarAI agent)
        {
            if (agent == null) return false;
            
            // 检查是否有有效的路径
            return agent.hasPath && !agent.pathPending;
        }
        
        /// <summary>
        /// 检查是否到达目的地
        /// </summary>
        public static bool HasReachedDestination(IAstarAI agent, float tolerance = 0.1f)
        {
            if (agent == null) return false;
            
            return agent.reachedDestination || agent.reachedEndOfPath;
        }
        
        /// <summary>
        /// 获取到目的地的距离
        /// </summary>
        public static float GetDistanceToDestination(IAstarAI agent)
        {
            if (agent == null) return float.MaxValue;
            
            return Vector3.Distance(agent.position, agent.destination);
        }
        
        /// <summary>
        /// 检查路径是否被阻塞
        /// </summary>
        public static bool IsPathBlocked(IAstarAI agent)
        {
            if (agent == null) return true;
            
            // 如果没有路径或路径计算失败，认为被阻塞
            return !agent.hasPath || agent.pathPending;
        }
        
        #endregion
        
        #region 寻路控制
        
        /// <summary>
        /// 设置目的地
        /// </summary>
        public static void SetDestination(IAstarAI agent, Vector3 destination)
        {
            if (agent == null) return;
            
            agent.destination = destination;
            agent.SearchPath();
            
            Debug.Log($"[PathfindingHelper] 设置目的地: {destination}");
        }
        
        /// <summary>
        /// 停止移动
        /// </summary>
        public static void StopMovement(IAstarAI agent)
        {
            if (agent == null) return;
            
            agent.isStopped = true;
            
            Debug.Log("[PathfindingHelper] 停止移动");
        }
        
        /// <summary>
        /// 恢复移动
        /// </summary>
        public static void ResumeMovement(IAstarAI agent)
        {
            if (agent == null) return;
            
            agent.isStopped = false;
            
            Debug.Log("[PathfindingHelper] 恢复移动");
        }
        
        /// <summary>
        /// 强制重新计算路径
        /// </summary>
        public static void RecalculatePath(IAstarAI agent)
        {
            if (agent == null) return;
            
            agent.SearchPath();
            
            Debug.Log("[PathfindingHelper] 强制重新计算路径");
        }
        
        #endregion
        
        #region 调试和工具
        
        /// <summary>
        /// 获取寻路代理状态信息
        /// </summary>
        public static string GetAgentStatusInfo(IAstarAI agent)
        {
            if (agent == null) return "寻路代理为空";
            
            return $"位置:{agent.position}, 目的地:{agent.destination}, " +
                   $"有路径:{agent.hasPath}, 路径计算中:{agent.pathPending}, " +
                   $"已到达:{agent.reachedDestination}, 停止:{agent.isStopped}, " +
                   $"速度:{agent.velocity.magnitude:F2}";
        }
        
        /// <summary>
        /// 检查A*寻路系统是否可用
        /// </summary>
        public static bool IsAstarSystemAvailable()
        {
            return AstarPath.active != null && AstarPath.active.isScanned;
        }
        
        /// <summary>
        /// 获取最近的可行走节点
        /// </summary>
        public static Vector3 GetNearestWalkablePosition(Vector3 position, float maxDistance = 10f)
        {
            if (!IsAstarSystemAvailable()) return position;
            
            var nearestNode = AstarPath.active.GetNearest(position, NNConstraint.Default);
            if (nearestNode.node != null && nearestNode.node.Walkable)
            {
                return (Vector3)nearestNode.position;
            }
            
            return position;
        }
        
        #endregion
    }
}
