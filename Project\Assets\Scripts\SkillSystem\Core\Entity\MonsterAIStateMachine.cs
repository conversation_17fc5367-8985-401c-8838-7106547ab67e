using UnityEngine;
using System.Collections.Generic;

namespace SkillSystem.Core
{
    /// <summary>
    /// 怪物AI状态机 - 管理怪物的各种AI状态和状态转换
    /// </summary>
    public class MonsterAIStateMachine
    {
        #region 状态定义
        
        public enum AIState
        {
            Idle,           // 空闲状态
            Patrol,         // 巡逻状态
            Searching,      // 搜索状态
            Chasing,        // 追击状态
            Attacking,      // 攻击状态
            Returning,      // 返回状态
            Stunned,        // 眩晕状态
            Dead            // 死亡状态
        }
        
        #endregion
        
        #region 状态数据
        
        private MonsterAIComponent owner;
        private AIState currentState;
        private AIState previousState;
        private float stateEnterTime;
        private Dictionary<AIState, IMonsterAIState> stateHandlers;
        
        #endregion
        
        #region 初始化
        
        /// <summary>
        /// 初始化状态机
        /// </summary>
        public void Initialize(MonsterAIComponent aiComponent)
        {
            owner = aiComponent;
            currentState = AIState.Idle;
            previousState = AIState.Idle;
            stateEnterTime = Time.time;
            
            // 初始化状态处理器
            InitializeStateHandlers();
            
            Debug.Log($"[MonsterAIStateMachine] 状态机初始化完成，初始状态: {currentState}");
        }
        
        /// <summary>
        /// 初始化状态处理器
        /// </summary>
        private void InitializeStateHandlers()
        {
            stateHandlers = new Dictionary<AIState, IMonsterAIState>
            {
                { AIState.Idle, new IdleState() },
                { AIState.Patrol, new PatrolState() },
                { AIState.Searching, new SearchingState() },
                { AIState.Chasing, new ChasingState() },
                { AIState.Attacking, new AttackingState() },
                { AIState.Returning, new ReturningState() },
                { AIState.Stunned, new StunnedState() },
                { AIState.Dead, new DeadState() }
            };
            
            // 初始化所有状态处理器
            foreach (var handler in stateHandlers.Values)
            {
                handler.Initialize(owner);
            }
        }
        
        #endregion
        
        #region 状态管理
        
        /// <summary>
        /// 更新状态机
        /// </summary>
        public void UpdateStateMachine(float deltaTime)
        {
            if (stateHandlers.TryGetValue(currentState, out var currentHandler))
            {
                // 更新当前状态
                var nextState = currentHandler.UpdateState(deltaTime);
                
                // 检查是否需要状态转换
                if (nextState != currentState)
                {
                    ChangeState(nextState);
                }
            }
        }
        
        /// <summary>
        /// 改变状态
        /// </summary>
        public void ChangeState(AIState newState)
        {
            if (currentState == newState) return;
            
            Debug.Log($"[MonsterAIStateMachine] 状态转换: {currentState} -> {newState}");
            
            // 退出当前状态
            if (stateHandlers.TryGetValue(currentState, out var currentHandler))
            {
                currentHandler.ExitState();
            }
            
            // 记录状态变化
            previousState = currentState;
            currentState = newState;
            stateEnterTime = Time.time;
            
            // 进入新状态
            if (stateHandlers.TryGetValue(newState, out var newHandler))
            {
                newHandler.EnterState(previousState);
            }
        }
        
        /// <summary>
        /// 强制设置状态
        /// </summary>
        public void ForceSetState(AIState state)
        {
            ChangeState(state);
        }
        
        #endregion
        
        #region 状态查询
        
        /// <summary>
        /// 获取当前状态
        /// </summary>
        public AIState GetCurrentState()
        {
            return currentState;
        }
        
        /// <summary>
        /// 获取上一个状态
        /// </summary>
        public AIState GetPreviousState()
        {
            return previousState;
        }
        
        /// <summary>
        /// 获取在当前状态的时间
        /// </summary>
        public float GetTimeInCurrentState()
        {
            return Time.time - stateEnterTime;
        }
        
        /// <summary>
        /// 检查是否在指定状态
        /// </summary>
        public bool IsInState(AIState state)
        {
            return currentState == state;
        }
        
        /// <summary>
        /// 检查是否可以转换到指定状态
        /// </summary>
        public bool CanTransitionTo(AIState targetState)
        {
            if (stateHandlers.TryGetValue(currentState, out var currentHandler))
            {
                return currentHandler.CanTransitionTo(targetState);
            }
            return false;
        }
        
        #endregion
        
        #region 销毁
        
        /// <summary>
        /// 销毁状态机
        /// </summary>
        public void Dispose()
        {
            // 退出当前状态
            if (stateHandlers.TryGetValue(currentState, out var currentHandler))
            {
                currentHandler.ExitState();
            }
            
            // 清理所有状态处理器
            foreach (var handler in stateHandlers.Values)
            {
                handler.Dispose();
            }
            
            stateHandlers.Clear();
            owner = null;
            
            Debug.Log("[MonsterAIStateMachine] 状态机已销毁");
        }
        
        #endregion
    }
    
    #region 状态接口
    
    /// <summary>
    /// 怪物AI状态接口
    /// </summary>
    public interface IMonsterAIState
    {
        /// <summary>
        /// 初始化状态
        /// </summary>
        void Initialize(MonsterAIComponent aiComponent);
        
        /// <summary>
        /// 进入状态
        /// </summary>
        void EnterState(MonsterAIStateMachine.AIState fromState);
        
        /// <summary>
        /// 更新状态
        /// </summary>
        MonsterAIStateMachine.AIState UpdateState(float deltaTime);
        
        /// <summary>
        /// 退出状态
        /// </summary>
        void ExitState();
        
        /// <summary>
        /// 检查是否可以转换到目标状态
        /// </summary>
        bool CanTransitionTo(MonsterAIStateMachine.AIState targetState);
        
        /// <summary>
        /// 销毁状态
        /// </summary>
        void Dispose();
    }
    
    #endregion
    
    #region 基础状态类
    
    /// <summary>
    /// 基础状态类 - 提供通用的状态功能
    /// </summary>
    public abstract class BaseMonsterAIState : IMonsterAIState
    {
        protected MonsterAIComponent aiComponent;
        protected float stateTime;
        
        public virtual void Initialize(MonsterAIComponent aiComponent)
        {
            this.aiComponent = aiComponent;
        }
        
        public virtual void EnterState(MonsterAIStateMachine.AIState fromState)
        {
            stateTime = 0f;
        }
        
        public abstract MonsterAIStateMachine.AIState UpdateState(float deltaTime);
        
        public virtual void ExitState()
        {
            // 基础退出逻辑
        }
        
        public virtual bool CanTransitionTo(MonsterAIStateMachine.AIState targetState)
        {
            // 默认允许转换到任何状态（除了当前状态）
            return true;
        }
        
        public virtual void Dispose()
        {
            aiComponent = null;
        }
        
        /// <summary>
        /// 更新状态时间
        /// </summary>
        protected void UpdateStateTime(float deltaTime)
        {
            stateTime += deltaTime;
        }
        
        /// <summary>
        /// 检查是否找到目标
        /// </summary>
        protected bool HasTarget()
        {
            return aiComponent?.GetCurrentTarget() != null;
        }
        
        /// <summary>
        /// 检查目标是否在攻击范围内
        /// </summary>
        protected bool IsTargetInAttackRange()
        {
            var target = aiComponent?.GetCurrentTarget();
            if (target == null) return false;
            
            float distance = Vector3.Distance(aiComponent.owner.Position, target.Position);
            return distance <= aiComponent.attackRange;
        }
        
        /// <summary>
        /// 检查目标是否在追击范围内
        /// </summary>
        protected bool IsTargetInChaseRange()
        {
            var target = aiComponent?.GetCurrentTarget();
            if (target == null) return false;
            
            float distance = Vector3.Distance(aiComponent.owner.Position, target.Position);
            return distance <= aiComponent.chaseRange;
        }
    }
    
    #endregion
}
