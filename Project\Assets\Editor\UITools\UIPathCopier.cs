using System.Collections.Generic;
using System.Text;
using UnityEditor;
using UnityEngine;
 public static class UIPathCopier
    {
        [MenuItem("GameObject/Copy Path", false, 0)]
        private static void CopyUIPath()
        {
            GameObject selectedObject = Selection.activeGameObject;
            if (selectedObject == null)
            {
                Debug.LogWarning("No GameObject selected!");
                return;
            }

            string path = GetGameObjectPath(selectedObject);
            CopyToClipboard(path);
            
            if (string.IsNullOrEmpty(path))
            {
                Debug.Log("Selected object is the root node, path is empty.");
            }
            else
            {
                Debug.Log($"Path copied to clipboard: {path}");
            }
        }

        [MenuItem("GameObject/Copy Path", true)]
        private static bool ValidateCopyUIPath()
        {
            GameObject selectedObject = Selection.activeGameObject;
            if (selectedObject == null)
                return false;

            return selectedObject.GetComponent<RectTransform>() != null;
        }

        private static string GetGameObjectPath(GameObject obj)
        {
            Transform current = obj.transform;
            
            // 找到实际的UI根节点（第一个不是Canvas (Environment)的节点）
            Transform uiRoot = FindUIRoot(current);
            
            // 如果选中的就是UI根节点，返回空字符串
            if (current == uiRoot)
            {
                return "";
            }

            // 构建从UI根节点到当前节点的路径（不包含根节点）
            List<string> pathElements = new List<string>();
            
            while (current != null && current != uiRoot)
            {
                pathElements.Insert(0, current.name);
                current = current.parent;
            }

            return string.Join("/", pathElements);
        }

        private static Transform FindUIRoot(Transform node)
        {
            Transform current = node;
            
            // 向上查找到最顶层
            while (current.parent != null)
            {
                current = current.parent;
            }
            
            // 如果顶层是Canvas (Environment)，返回它的第一个子节点
            if (IsSystemCanvas(current))
            {
                if (current.childCount > 0)
                {
                    return current.GetChild(0);
                }
            }
            
            // 否则返回找到的根节点
            return current;
        }

        private static bool IsSystemCanvas(Transform transform)
        {
            if (transform == null) return false;
            
            Canvas canvas = transform.GetComponent<Canvas>();
            if (canvas == null) return false;
            
            // Unity生成的Canvas通常包含"Environment"
            return transform.name.Contains("Environment") || 
                   transform.name == "Canvas (Environment)";
        }

        private static void CopyToClipboard(string text)
        {
            GUIUtility.systemCopyBuffer = text;
        }
    }