using UnityEngine;
using SkillSystem.Core;
using Cysharp.Threading.Tasks;
using System.Collections.Generic;

/// <summary>
/// 怪物AI系统测试脚本
/// </summary>
public class MonsterAITest : MonoBehaviour
{
    [Header("测试配置")]
    public bool enableTest = true;
    public int testMonsterCount = 3;
    public float spawnRadius = 10f;
    public Vector3 spawnCenter = Vector3.zero;
    public int monsterEntityCfgId = 2; // 怪物配置ID
    
    [Header("测试控制")]
    public bool showDebugInfo = true;
    public bool enableAIDebug = true;
    public float debugUpdateInterval = 1f;
    
    [Header("运行时信息")]
    public List<MonsterEntity> testMonsters = new List<MonsterEntity>();
    public bool isTestRunning = false;
    
    private float lastDebugTime = 0f;
    
    #region Unity生命周期
    
    private void Start()
    {
        if (enableTest)
        {
            StartTest();
        }
    }
    
    private void Update()
    {
        if (!isTestRunning) return;
        
        // 更新调试信息
        if (showDebugInfo && Time.time - lastDebugTime >= debugUpdateInterval)
        {
            UpdateDebugInfo();
            lastDebugTime = Time.time;
        }
        
        // 处理输入
        HandleInput();
    }
    
    private void OnGUI()
    {
        if (!isTestRunning) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 600));
        GUILayout.Label("=== 怪物AI系统测试 ===", GUI.skin.box);
        
        // 显示管理器状态
        if (MonsterManager.Instance != null)
        {
            GUILayout.Label($"MonsterManager状态: {MonsterManager.Instance.GetManagerStatus()}");
            
            var stats = MonsterManager.Instance.GetStatistics();
            GUILayout.Label($"统计信息: {stats.ToString()}");
        }
        
        // 显示怪物状态
        GUILayout.Label("=== 怪物状态 ===");
        for (int i = 0; i < testMonsters.Count; i++)
        {
            var monster = testMonsters[i];
            if (monster != null)
            {
                string status = monster.GetAIStatus();
                GUILayout.Label($"怪物{i}: {status}");
            }
        }
        
        // 控制按钮
        GUILayout.Space(10);
        GUILayout.Label("=== 控制面板 ===");
        
        if (GUILayout.Button("暂停所有AI"))
        {
            MonsterManager.Instance?.PauseAllMonsterAI();
        }
        
        if (GUILayout.Button("恢复所有AI"))
        {
            MonsterManager.Instance?.ResumeAllMonsterAI();
        }
        
        if (GUILayout.Button("打印详细状态"))
        {
            MonsterManager.Instance?.PrintAllMonsterStatus();
        }
        
        if (GUILayout.Button("销毁所有怪物"))
        {
            DestroyAllTestMonsters();
        }
        
        if (GUILayout.Button("重新生成怪物"))
        {
            RestartTest();
        }
        
        GUILayout.EndArea();
    }
    
    #endregion
    
    #region 测试控制
    
    /// <summary>
    /// 开始测试
    /// </summary>
    public async void StartTest()
    {
        Debug.Log("[MonsterAITest] 开始怪物AI系统测试");
        
        // 确保管理器已初始化
        if (MonsterManager.Instance == null)
        {
            Debug.LogError("[MonsterAITest] MonsterManager未初始化");
            return;
        }
        
        // 设置调试模式
        MonsterManager.Instance.SetDebugMode(enableAIDebug, showDebugInfo);
        
        // 生成测试怪物
        await SpawnTestMonsters();
        
        isTestRunning = true;
        Debug.Log("[MonsterAITest] 测试开始运行");
    }
    
    /// <summary>
    /// 停止测试
    /// </summary>
    public void StopTest()
    {
        Debug.Log("[MonsterAITest] 停止怪物AI系统测试");
        
        DestroyAllTestMonsters();
        isTestRunning = false;
    }
    
    /// <summary>
    /// 重新开始测试
    /// </summary>
    public async void RestartTest()
    {
        StopTest();
        await UniTask.Delay(1000); // 等待1秒
        StartTest();
    }
    
    #endregion
    
    #region 怪物生成
    
    /// <summary>
    /// 生成测试怪物
    /// </summary>
    private async UniTask SpawnTestMonsters()
    {
        testMonsters.Clear();
        
        for (int i = 0; i < testMonsterCount; i++)
        {
            // 随机生成位置
            Vector2 randomCircle = Random.insideUnitCircle * spawnRadius;
            Vector3 spawnPosition = spawnCenter + new Vector3(randomCircle.x, 0, randomCircle.y);
            
            // 确保位置可行走
            spawnPosition = PathfindingHelper.GetNearestWalkablePosition(spawnPosition);
            
            // 创建怪物
            var monster = await MonsterManager.Instance.CreateMonster(monsterEntityCfgId, spawnPosition);
            
            if (monster != null)
            {
                testMonsters.Add(monster);
                
                // 配置怪物AI参数
                ConfigureMonsterAI(monster, i);
                
                Debug.Log($"[MonsterAITest] 生成测试怪物 {i}: {monster.UniqueId} 位置: {spawnPosition}");
            }
            else
            {
                Debug.LogError($"[MonsterAITest] 生成怪物 {i} 失败");
            }
        }
        
        Debug.Log($"[MonsterAITest] 成功生成 {testMonsters.Count}/{testMonsterCount} 个测试怪物");
    }
    
    /// <summary>
    /// 配置怪物AI参数
    /// </summary>
    private void ConfigureMonsterAI(MonsterEntity monster, int index)
    {
        var aiComponent = monster.GetAIComponent();
        if (aiComponent == null) return;
        
        // 根据索引设置不同的AI参数
        switch (index % 3)
        {
            case 0: // 激进型
                aiComponent.detectionRange = 20f;
                aiComponent.chaseRange = 25f;
                aiComponent.attackRange = 3f;
                aiComponent.moveSpeed = 4f;
                aiComponent.skillReleaseInterval = 1.5f;
                break;
                
            case 1: // 保守型
                aiComponent.detectionRange = 10f;
                aiComponent.chaseRange = 15f;
                aiComponent.attackRange = 2f;
                aiComponent.moveSpeed = 2f;
                aiComponent.skillReleaseInterval = 3f;
                break;
                
            case 2: // 平衡型
                aiComponent.detectionRange = 15f;
                aiComponent.chaseRange = 20f;
                aiComponent.attackRange = 2.5f;
                aiComponent.moveSpeed = 3f;
                aiComponent.skillReleaseInterval = 2f;
                break;
        }
        
        Debug.Log($"[MonsterAITest] 配置怪物 {index} AI参数完成");
    }
    
    /// <summary>
    /// 销毁所有测试怪物
    /// </summary>
    private void DestroyAllTestMonsters()
    {
        foreach (var monster in testMonsters)
        {
            if (monster != null)
            {
                MonsterManager.Instance?.DestroyMonster(monster);
            }
        }
        
        testMonsters.Clear();
        Debug.Log("[MonsterAITest] 销毁所有测试怪物完成");
    }
    
    #endregion
    
    #region 调试和输入处理
    
    /// <summary>
    /// 更新调试信息
    /// </summary>
    private void UpdateDebugInfo()
    {
        if (MonsterManager.Instance == null) return;
        
        // 定期打印状态信息
        if (enableAIDebug)
        {
            Debug.Log($"[MonsterAITest] {MonsterManager.Instance.GetManagerStatus()}");
        }
    }
    
    /// <summary>
    /// 处理输入
    /// </summary>
    private void HandleInput()
    {
        // 按键控制
        if (Input.GetKeyDown(KeyCode.P))
        {
            // 暂停/恢复AI
            if (MonsterManager.Instance != null)
            {
                bool currentState = MonsterManager.Instance.enableGlobalAI;
                MonsterManager.Instance.SetGlobalAIEnabled(!currentState);
                Debug.Log($"[MonsterAITest] 全局AI状态切换为: {!currentState}");
            }
        }
        
        if (Input.GetKeyDown(KeyCode.R))
        {
            // 重新开始测试
            RestartTest();
        }
        
        if (Input.GetKeyDown(KeyCode.S))
        {
            // 打印状态
            MonsterManager.Instance?.PrintAllMonsterStatus();
        }
        
        if (Input.GetKeyDown(KeyCode.T))
        {
            // 让所有怪物攻击英雄
            var hero = EntityManager.Instance?.HeroEntity;
            if (hero != null)
            {
                MonsterManager.Instance?.SetAllMonstersTarget(hero);
                Debug.Log("[MonsterAITest] 设置所有怪物攻击英雄");
            }
        }
    }
    
    #endregion
    
    #region 编辑器辅助
    
    private void OnDrawGizmosSelected()
    {
        // 绘制生成范围
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(spawnCenter, spawnRadius);
        
        // 绘制怪物位置
        Gizmos.color = Color.red;
        foreach (var monster in testMonsters)
        {
            if (monster != null && monster.entityObject != null)
            {
                Gizmos.DrawWireSphere(monster.Position, 1f);
            }
        }
    }
    
    #endregion
}
