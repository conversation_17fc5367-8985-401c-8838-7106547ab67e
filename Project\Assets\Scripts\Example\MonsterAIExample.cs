using UnityEngine;
using SkillSystem.Core;
using Cysharp.Threading.Tasks;

/// <summary>
/// 怪物AI系统使用示例
/// 演示如何创建和配置怪物AI
/// </summary>
public class MonsterAIExample : MonoBehaviour
{
    [Header("示例配置")]
    public bool autoStart = true;
    public int monsterEntityCfgId = 2;
    public Vector3 spawnPosition = Vector3.zero;
    
    [Header("AI配置")]
    public float detectionRange = 15f;
    public float attackRange = 3f;
    public float chaseRange = 20f;
    public float moveSpeed = 3f;
    public float skillInterval = 2f;
    
    private MonsterEntity exampleMonster;
    
    private async void Start()
    {
        if (autoStart)
        {
            await CreateExampleMonster();
        }
    }
    
    /// <summary>
    /// 创建示例怪物
    /// </summary>
    public async UniTask CreateExampleMonster()
    {
        Debug.Log("[MonsterAIExample] 开始创建示例怪物");
        
        try
        {
            // 1. 创建怪物实体
            exampleMonster = await MonsterManager.Instance.CreateMonster(
                monsterEntityCfgId, 
                spawnPosition
            );
            
            if (exampleMonster == null)
            {
                Debug.LogError("[MonsterAIExample] 创建怪物失败");
                return;
            }
            
            // 2. 配置AI参数
            ConfigureMonsterAI();
            
            // 3. 设置初始目标（如果有英雄）
            SetInitialTarget();
            
            Debug.Log($"[MonsterAIExample] 示例怪物创建完成: {exampleMonster.UniqueId}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[MonsterAIExample] 创建怪物异常: {e.Message}");
        }
    }
    
    /// <summary>
    /// 配置怪物AI参数
    /// </summary>
    private void ConfigureMonsterAI()
    {
        var aiComponent = exampleMonster.GetAIComponent();
        if (aiComponent == null)
        {
            Debug.LogError("[MonsterAIExample] 获取AI组件失败");
            return;
        }
        
        // 配置检测和攻击范围
        aiComponent.detectionRange = detectionRange;
        aiComponent.attackRange = attackRange;
        aiComponent.chaseRange = chaseRange;
        
        // 配置移动参数
        aiComponent.moveSpeed = moveSpeed;
        aiComponent.rotationSpeed = 180f;
        aiComponent.stopDistance = 1f;
        
        // 配置技能释放
        aiComponent.enableAutoSkill = true;
        aiComponent.skillReleaseInterval = skillInterval;
        aiComponent.prioritizeCloseTargets = true;
        
        // 启用寻路
        aiComponent.usePathfinding = true;
        
        // 启用AI
        aiComponent.enableAI = true;
        
        Debug.Log("[MonsterAIExample] AI参数配置完成");
    }
    
    /// <summary>
    /// 设置初始目标
    /// </summary>
    private void SetInitialTarget()
    {
        // 尝试获取英雄作为目标
        var hero = EntityManager.Instance?.HeroEntity;
        if (hero != null)
        {
            exampleMonster.SetAITarget(hero);
            Debug.Log("[MonsterAIExample] 设置英雄为目标");
        }
        else
        {
            Debug.Log("[MonsterAIExample] 未找到英雄，怪物将自动搜索目标");
        }
    }
    
    /// <summary>
    /// 销毁示例怪物
    /// </summary>
    public void DestroyExampleMonster()
    {
        if (exampleMonster != null)
        {
            MonsterManager.Instance?.DestroyMonster(exampleMonster);
            exampleMonster = null;
            Debug.Log("[MonsterAIExample] 示例怪物已销毁");
        }
    }
    
    /// <summary>
    /// 切换AI状态
    /// </summary>
    public void ToggleAI()
    {
        if (exampleMonster != null)
        {
            var aiComponent = exampleMonster.GetAIComponent();
            if (aiComponent != null)
            {
                bool currentState = aiComponent.enableAI;
                exampleMonster.SetAIEnabled(!currentState);
                Debug.Log($"[MonsterAIExample] AI状态切换为: {!currentState}");
            }
        }
    }
    
    /// <summary>
    /// 设置眩晕状态
    /// </summary>
    public void StunMonster(float duration = 3f)
    {
        if (exampleMonster != null)
        {
            var aiComponent = exampleMonster.GetAIComponent();
            aiComponent?.SetStunned(duration);
            Debug.Log($"[MonsterAIExample] 怪物被眩晕 {duration} 秒");
        }
    }
    
    /// <summary>
    /// 打印怪物状态
    /// </summary>
    public void PrintMonsterStatus()
    {
        if (exampleMonster != null)
        {
            string status = exampleMonster.GetAIStatus();
            Debug.Log($"[MonsterAIExample] 怪物状态: {status}");
        }
        else
        {
            Debug.Log("[MonsterAIExample] 没有示例怪物");
        }
    }
    
    #region Unity编辑器按钮
    
    [ContextMenu("创建示例怪物")]
    private async void CreateMonsterFromMenu()
    {
        await CreateExampleMonster();
    }
    
    [ContextMenu("销毁示例怪物")]
    private void DestroyMonsterFromMenu()
    {
        DestroyExampleMonster();
    }
    
    [ContextMenu("切换AI状态")]
    private void ToggleAIFromMenu()
    {
        ToggleAI();
    }
    
    [ContextMenu("眩晕怪物")]
    private void StunMonsterFromMenu()
    {
        StunMonster();
    }
    
    [ContextMenu("打印状态")]
    private void PrintStatusFromMenu()
    {
        PrintMonsterStatus();
    }
    
    #endregion
    
    #region 调试绘制
    
    private void OnDrawGizmosSelected()
    {
        if (exampleMonster == null) return;
        
        Vector3 position = exampleMonster.Position;
        
        // 绘制检测范围
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(position, detectionRange);
        
        // 绘制追击范围
        Gizmos.color = Color.orange;
        Gizmos.DrawWireSphere(position, chaseRange);
        
        // 绘制攻击范围
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(position, attackRange);
        
        // 绘制到目标的连线
        var aiComponent = exampleMonster.GetAIComponent();
        var target = aiComponent?.GetCurrentTarget();
        if (target != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawLine(position, target.Position);
        }
    }
    
    #endregion
    
    #region GUI显示
    
    private void OnGUI()
    {
        if (exampleMonster == null) return;
        
        GUILayout.BeginArea(new Rect(Screen.width - 300, 10, 290, 200));
        GUILayout.Label("=== 怪物AI示例控制 ===", GUI.skin.box);
        
        if (GUILayout.Button("切换AI状态"))
        {
            ToggleAI();
        }
        
        if (GUILayout.Button("眩晕怪物(3秒)"))
        {
            StunMonster(3f);
        }
        
        if (GUILayout.Button("打印状态"))
        {
            PrintMonsterStatus();
        }
        
        if (GUILayout.Button("销毁怪物"))
        {
            DestroyExampleMonster();
        }
        
        // 显示当前状态
        var aiComponent = exampleMonster.GetAIComponent();
        if (aiComponent != null)
        {
            GUILayout.Label($"当前状态: {aiComponent.GetCurrentState()}");
            GUILayout.Label($"AI启用: {aiComponent.enableAI}");
            
            var target = aiComponent.GetCurrentTarget();
            string targetInfo = target != null ? $"目标: {target.UniqueId}" : "无目标";
            GUILayout.Label(targetInfo);
        }
        
        GUILayout.EndArea();
    }
    
    #endregion
}
