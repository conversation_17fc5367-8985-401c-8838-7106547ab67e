%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 8
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientEquatorColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientGroundColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_TemporalCoherenceThreshold: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 9
    m_Resolution: 1
    m_BakeResolution: 50
    m_TextureWidth: 1024
    m_TextureHeight: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 0
    m_CompAOExponentDirect: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 0
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 1024
    m_ReflectionCompression: 2
    m_MixedBakeMode: 1
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVRFilteringMode: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &5
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666666
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &103545017
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 103545018}
  - component: {fileID: 103545020}
  - component: {fileID: 103545019}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &103545018
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 103545017}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 896607774}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &103545019
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 103545017}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Animated insert/remove
--- !u!222 &103545020
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 103545017}
--- !u!1 &103642501
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 103642502}
  - component: {fileID: 103642505}
  - component: {fileID: 103642504}
  - component: {fileID: 103642503}
  m_Layer: 5
  m_Name: Note2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &103642502
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 103642501}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1303894993}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.019285182, y: 0.012352529}
  m_AnchorMax: {x: 0.5, y: 0.042945534}
  m_AnchoredPosition: {x: -1, y: 1}
  m_SizeDelta: {x: 2, y: 1}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &103642503
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 103642501}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1573420865, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!114 &103642504
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 103642501}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 13
    m_FontStyle: 0
    m_BestFit: 1
    m_MinSize: 9
    m_MaxSize: 13
    m_Alignment: 3
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: '* If upgrading from versions prior to 4.3, check release notes'
--- !u!222 &103642505
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 103642501}
--- !u!1 &213964103
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 213964104}
  - component: {fileID: 213964106}
  - component: {fileID: 213964105}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &213964104
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 213964103}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1488514221}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &213964105
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 213964103}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Nested ScrollViews
--- !u!222 &213964106
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 213964103}
--- !u!1 &228830446
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 228830447}
  - component: {fileID: 228830451}
  - component: {fileID: 228830450}
  - component: {fileID: 228830449}
  - component: {fileID: 228830448}
  m_Layer: 0
  m_Name: NestedScrollViewsTabbed
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &228830447
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 228830446}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.9999984, y: 0.9999989, z: 0.9999989}
  m_Children:
  - {fileID: 531583328}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &228830448
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 228830446}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: simple_nested_scrollviews_tabbed
  allSceneNamesInOrder: []
--- !u!114 &228830449
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 228830446}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 228830450}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &228830450
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 228830446}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &228830451
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 228830446}
--- !u!1 &291180266
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 291180267}
  - component: {fileID: 291180269}
  - component: {fileID: 291180268}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &291180267
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 291180266}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1516315085}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &291180268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 291180266}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Main example
--- !u!222 &291180269
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 291180266}
--- !u!1 &296758052
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 296758053}
  - component: {fileID: 296758055}
  - component: {fileID: 296758054}
  - component: {fileID: 296758056}
  m_Layer: 5
  m_Name: SelectedIndicatorText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &296758053
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 296758052}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1303894993}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.7451427}
  m_AnchorMax: {x: 0.5, y: 0.7451427}
  m_AnchoredPosition: {x: 0, y: 3}
  m_SizeDelta: {x: 299, y: 27}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &296758054
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 296758052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 18
    m_FontStyle: 0
    m_BestFit: 1
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Select a demo:'
--- !u!222 &296758055
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 296758052}
--- !u!114 &296758056
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 296758052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1573420865, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!1 &323595599
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 323595600}
  - component: {fileID: 323595602}
  - component: {fileID: 323595601}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &323595600
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 323595599}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1001532910}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &323595601
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 323595599}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: 'Nested ScrollViews

    (same direction)'
--- !u!222 &323595602
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 323595599}
--- !u!1 &358732457
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 358732458}
  - component: {fileID: 358732460}
  - component: {fileID: 358732459}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &358732458
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 358732457}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 689793890}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &358732459
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 358732457}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Popup ^
--- !u!222 &358732460
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 358732457}
--- !u!1 &358998811
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 358998812}
  - component: {fileID: 358998816}
  - component: {fileID: 358998815}
  - component: {fileID: 358998814}
  - component: {fileID: 358998813}
  m_Layer: 0
  m_Name: HierarchyStickyHeaders
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &358998812
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 358998811}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1542415220}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &358998813
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 358998811}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: hierarchy_or_treeview_sticky_header
  allSceneNamesInOrder: []
--- !u!114 &358998814
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 358998811}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 358998815}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &358998815
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 358998811}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &358998816
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 358998811}
--- !u!1 &369320399
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 369320400}
  - component: {fileID: 369320404}
  - component: {fileID: 369320403}
  - component: {fileID: 369320402}
  - component: {fileID: 369320401}
  m_Layer: 0
  m_Name: Chat
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &369320400
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 369320399}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1940838231}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 18
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &369320401
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 369320399}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: chat
  allSceneNamesInOrder: []
--- !u!114 &369320402
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 369320399}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 369320403}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &369320403
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 369320399}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &369320404
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 369320399}
--- !u!1 &383434206
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 383434207}
  - component: {fileID: 383434210}
  - component: {fileID: 383434209}
  - component: {fileID: 383434208}
  m_Layer: 5
  m_Name: ScreenOrientationNoteText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &383434207
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 383434206}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1303894993}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.57100004, y: 0}
  m_AnchorMax: {x: 0.98300004, y: 0.060000002}
  m_AnchoredPosition: {x: 0, y: 0.5999756}
  m_SizeDelta: {x: -1, y: 1.3000002}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &383434208
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 383434206}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1573420865, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!114 &383434209
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 383434206}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 17
    m_FontStyle: 0
    m_BestFit: 1
    m_MinSize: 10
    m_MaxSize: 23
    m_Alignment: 5
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Psst: Rotate to landscape mode'
--- !u!222 &383434210
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 383434206}
--- !u!1 &400772333
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 400772334}
  - component: {fileID: 400772336}
  - component: {fileID: 400772335}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &400772334
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 400772333}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 826429639}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &400772335
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 400772333}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Simple
--- !u!222 &400772336
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 400772333}
--- !u!1 &418059327
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 418059330}
  - component: {fileID: 418059329}
  - component: {fileID: 418059328}
  m_Layer: 5
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &418059328
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 418059327}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85490197, g: 0.99607843, b: 0.99607843, a: 0.6784314}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 21300000, guid: 27193086681062a4b8cd1eb34c6c1881, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &418059329
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 418059327}
--- !u!224 &418059330
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 418059327}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1303894993}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 1}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: -328, y: -63}
  m_SizeDelta: {x: 73.9, y: 9.2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &433294951
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 433294952}
  - component: {fileID: 433294954}
  - component: {fileID: 433294953}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &433294952
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 433294951}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1730056692}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &433294953
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 433294951}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: TableView
--- !u!222 &433294954
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 433294951}
--- !u!1 &461957006
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 461957007}
  - component: {fileID: 461957009}
  - component: {fileID: 461957008}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &461957007
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 461957006}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1644985581}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &461957008
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 461957006}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Multiple prefabs
--- !u!222 &461957009
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 461957006}
--- !u!1 &482446898
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 482446899}
  - component: {fileID: 482446903}
  - component: {fileID: 482446902}
  - component: {fileID: 482446901}
  - component: {fileID: 482446900}
  m_Layer: 0
  m_Name: Hierarchy
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &482446899
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 482446898}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1160994560}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &482446900
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 482446898}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: hierarchy_or_treeview
  allSceneNamesInOrder: []
--- !u!114 &482446901
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 482446898}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 482446902}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &482446902
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 482446898}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &482446903
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 482446898}
--- !u!1 &523245125
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 523245126}
  - component: {fileID: 523245128}
  - component: {fileID: 523245127}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &523245126
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 523245125}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1214215661}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &523245127
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 523245125}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Looping spinner
--- !u!222 &523245128
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 523245125}
--- !u!1 &531583327
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 531583328}
  - component: {fileID: 531583330}
  - component: {fileID: 531583329}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &531583328
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 531583327}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 228830447}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &531583329
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 531583327}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: 'Nested ScrollViews

    (tabbed)'
--- !u!222 &531583330
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 531583327}
--- !u!1 &540795018
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 540795019}
  - component: {fileID: 540795021}
  - component: {fileID: 540795020}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &540795019
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 540795018}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1732247403}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &540795020
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 540795018}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Grid packed
--- !u!222 &540795021
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 540795018}
--- !u!1 &582046076
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 582046077}
  - component: {fileID: 582046079}
  - component: {fileID: 582046078}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &582046077
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 582046076}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1198710708}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &582046078
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 582046076}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Filterable data
--- !u!222 &582046079
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 582046076}
--- !u!1 &620858259
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 620858260}
  - component: {fileID: 620858262}
  - component: {fileID: 620858261}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &620858260
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 620858259}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 762738917}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &620858261
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 620858259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Incremental item fetching
--- !u!222 &620858262
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 620858259}
--- !u!1001 &663717106
Prefab:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1241920638}
    m_Modifications:
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchoredPosition.x
      value: -115.49899
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_SizeDelta.x
      value: 231.002
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchorMin.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchorMin.y
      value: 0.9556667
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 22450426, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450426, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450426, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450426, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450426, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450426, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450424, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450424, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450424, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450424, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450424, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22450424, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 100100000, guid: 9ef2faede4b04a946b28eef8a8ab244c, type: 2}
  m_IsPrefabParent: 0
--- !u!1 &689793889
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 689793890}
  - component: {fileID: 689793894}
  - component: {fileID: 689793893}
  - component: {fileID: 689793892}
  - component: {fileID: 689793891}
  m_Layer: 0
  m_Name: DateTimePickerPopUpButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &689793890
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 689793889}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 358732458}
  m_Father: {fileID: 1828121640}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.6599746, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &689793891
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 689793889}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc66ae71e4afb0a4ebff7a12329fc8e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &689793892
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 689793889}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 689793893}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &689793893
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 689793889}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &689793894
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 689793889}
--- !u!1 &708379261
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 708379266}
  - component: {fileID: 708379265}
  - component: {fileID: 708379263}
  - component: {fileID: 708379262}
  - component: {fileID: 708379267}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &708379262
AudioListener:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 708379261}
  m_Enabled: 1
--- !u!124 &708379263
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 708379261}
  m_Enabled: 1
--- !u!20 &708379265
Camera:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 708379261}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0.019607844}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &708379266
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 708379261}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &708379267
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 708379261}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 29e926f221368614b8c1e29a67af481b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Text: {fileID: 0}
  setTargetFPSTo60: 1
--- !u!1 &762738916
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 762738917}
  - component: {fileID: 762738921}
  - component: {fileID: 762738920}
  - component: {fileID: 762738919}
  - component: {fileID: 762738918}
  m_Layer: 0
  m_Name: IncrementalItemFetch
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &762738917
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 762738916}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 620858260}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &762738918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 762738916}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: incremental_item_fetching
  allSceneNamesInOrder: []
--- !u!114 &762738919
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 762738916}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 762738920}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &762738920
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 762738916}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &762738921
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 762738916}
--- !u!1 &826429638
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 826429639}
  - component: {fileID: 826429643}
  - component: {fileID: 826429642}
  - component: {fileID: 826429641}
  - component: {fileID: 826429640}
  m_Layer: 0
  m_Name: SimpleButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &826429639
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 826429638}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 400772334}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 24
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &826429640
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 826429638}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: simple_tutorial
  allSceneNamesInOrder: []
--- !u!114 &826429641
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 826429638}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 826429642}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &826429642
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 826429638}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &826429643
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 826429638}
--- !u!1 &885488802
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 885488803}
  - component: {fileID: 885488807}
  - component: {fileID: 885488806}
  - component: {fileID: 885488805}
  - component: {fileID: 885488804}
  m_Layer: 0
  m_Name: DifferentPrefabPerOrientationButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &885488803
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 885488802}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.9999984, y: 0.9999989, z: 0.9999989}
  m_Children:
  - {fileID: 1855402676}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 21
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &885488804
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 885488802}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: different_prefab_per_orientation
  allSceneNamesInOrder: []
--- !u!114 &885488805
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 885488802}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 885488806}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &885488806
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 885488802}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &885488807
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 885488802}
--- !u!1 &896607773
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 896607774}
  - component: {fileID: 896607778}
  - component: {fileID: 896607777}
  - component: {fileID: 896607776}
  - component: {fileID: 896607775}
  m_Layer: 0
  m_Name: AnimatedInsertRemoveButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &896607774
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 896607773}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 103545018}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 23
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &896607775
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 896607773}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: animated_insert_remove
  allSceneNamesInOrder: []
--- !u!114 &896607776
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 896607773}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 896607777}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &896607777
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 896607773}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &896607778
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 896607773}
--- !u!1 &919778731
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 919778732}
  - component: {fileID: 919778736}
  - component: {fileID: 919778735}
  - component: {fileID: 919778734}
  - component: {fileID: 919778733}
  m_Layer: 0
  m_Name: GridPlusAsync
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &919778732
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919778731}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1640136425}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &919778733
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919778731}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: grid_plus_horizontal_plus_async_download
  allSceneNamesInOrder: []
--- !u!114 &919778734
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919778731}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 919778735}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &919778735
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919778731}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &919778736
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 919778731}
--- !u!1 &974008611
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 974008612}
  - component: {fileID: 974008614}
  - component: {fileID: 974008613}
  m_Layer: 5
  m_Name: Image
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &974008612
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 974008611}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1303894993}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 1}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: 329, y: -63}
  m_SizeDelta: {x: 77.2, y: 9.2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &974008613
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 974008611}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85490197, g: 0.99607843, b: 0.99607843, a: 0.6784314}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 21300000, guid: 27193086681062a4b8cd1eb34c6c1881, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &974008614
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 974008611}
--- !u!1 &974677809
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 974677810}
  - component: {fileID: 974677813}
  - component: {fileID: 974677812}
  - component: {fileID: 974677811}
  - component: {fileID: 974677814}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &974677810
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 974677809}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1303894993}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 1}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: 0, y: -62}
  m_SizeDelta: {x: 581, y: 109}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &974677811
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 974677809}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1573420865, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 0.5}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!114 &974677812
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 974677809}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.83823526, g: 0.83823526, b: 0.83823526, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 87416143913912b45858aa5c537ff097, type: 3}
    m_FontSize: 25
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Optimized ScrollView Adapter vX.X.X
--- !u!222 &974677813
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 974677809}
--- !u!114 &974677814
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 974677809}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0744cda75a458c049ba0e67a3e5b5ecd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &991845379
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 991845380}
  - component: {fileID: 991845382}
  - component: {fileID: 991845381}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &991845380
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 991845379}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1482459723}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &991845381
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 991845379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: "Content size fitter + \nresize window"
--- !u!222 &991845382
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 991845379}
--- !u!1 &1001532909
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1001532910}
  - component: {fileID: 1001532914}
  - component: {fileID: 1001532913}
  - component: {fileID: 1001532912}
  - component: {fileID: 1001532911}
  m_Layer: 0
  m_Name: NestedSameDirectionButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1001532910
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1001532909}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 323595600}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1001532911
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1001532909}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: nested_scrollviews_same_direction
  allSceneNamesInOrder: []
--- !u!114 &1001532912
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1001532909}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1001532913}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1001532913
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1001532909}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1001532914
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1001532909}
--- !u!1 &1027745067
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1027745071}
  - component: {fileID: 1027745070}
  - component: {fileID: 1027745069}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1027745069
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1027745067}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1077351063, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 1
--- !u!114 &1027745070
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1027745067}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -619905303, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 5
--- !u!4 &1027745071
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1027745067}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1097883688
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1097883689}
  - component: {fileID: 1097883691}
  - component: {fileID: 1097883690}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1097883689
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1097883688}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2026738394}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1097883690
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1097883688}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Page view
--- !u!222 &1097883691
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1097883688}
--- !u!1 &1109325037
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1109325038}
  - component: {fileID: 1109325040}
  - component: {fileID: 1109325039}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1109325038
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1109325037}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1909775993}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1109325039
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1109325037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 0.41960785}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Lite (soon)
--- !u!222 &1109325040
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1109325037}
--- !u!1 &1160994559
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1160994560}
  - component: {fileID: 1160994562}
  - component: {fileID: 1160994561}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1160994560
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1160994559}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 482446899}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1160994561
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1160994559}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Hierarchy (aka TreeView)
--- !u!222 &1160994562
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1160994559}
--- !u!1 &1198710707
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1198710708}
  - component: {fileID: 1198710712}
  - component: {fileID: 1198710711}
  - component: {fileID: 1198710710}
  - component: {fileID: 1198710709}
  m_Layer: 0
  m_Name: FilterableDataButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1198710708
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1198710707}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.99999607, y: 0.99999654, z: 0.99999654}
  m_Children:
  - {fileID: 582046077}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 22
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1198710709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1198710707}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: filterable_data
  allSceneNamesInOrder: []
--- !u!114 &1198710710
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1198710707}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1198710711}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1198710711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1198710707}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1198710712
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1198710707}
--- !u!1 &1214215660
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1214215661}
  - component: {fileID: 1214215665}
  - component: {fileID: 1214215664}
  - component: {fileID: 1214215663}
  - component: {fileID: 1214215662}
  m_Layer: 0
  m_Name: LoopingSpinner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1214215661
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1214215660}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 523245126}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 16
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1214215662
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1214215660}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: looping_spinner
  allSceneNamesInOrder: []
--- !u!114 &1214215663
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1214215660}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1214215664}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1214215664
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1214215660}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1214215665
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1214215660}
--- !u!1 &1241920637
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1241920638}
  - component: {fileID: 1241920641}
  - component: {fileID: 1241920640}
  - component: {fileID: 1241920639}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1241920638
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1241920637}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_Children:
  - {fileID: 1303894993}
  - {fileID: 1630309479}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &1241920639
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1241920637}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1301386320, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1241920640
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1241920637}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1980459831, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 1
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
--- !u!223 &1241920641
Canvas:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1241920637}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &1303894992
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1303894993}
  - component: {fileID: 1303894995}
  - component: {fileID: 1303894994}
  m_Layer: 5
  m_Name: Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1303894993
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1303894992}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1470224315}
  - {fileID: 418059330}
  - {fileID: 296758053}
  - {fileID: 974008612}
  - {fileID: 974677810}
  - {fileID: 1568887591}
  - {fileID: 383434207}
  - {fileID: 103642502}
  m_Father: {fileID: 1241920638}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: -0.000061035}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1303894994
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1303894992}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bf318e3d1c1192c4dbf6de29f0728796, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  target: {fileID: 383434206}
--- !u!222 &1303894995
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1303894992}
--- !u!1 &1361331595
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1361331596}
  - component: {fileID: 1361331600}
  - component: {fileID: 1361331599}
  - component: {fileID: 1361331598}
  - component: {fileID: 1361331597}
  m_Layer: 0
  m_Name: PullToRefresh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1361331596
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1361331595}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1875792098}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 17
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1361331597
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1361331595}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: simple_pull_to_refresh
  allSceneNamesInOrder: []
--- !u!114 &1361331598
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1361331595}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1361331599}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1361331599
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1361331595}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1361331600
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1361331595}
--- !u!1 &1382893869
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1382893870}
  - component: {fileID: 1382893874}
  - component: {fileID: 1382893873}
  - component: {fileID: 1382893872}
  - component: {fileID: 1382893871}
  m_Layer: 0
  m_Name: ExpandToVariableSizeButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1382893870
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1382893869}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1578989384}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 20
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1382893871
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1382893869}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: expand_item_to_variable_size
  allSceneNamesInOrder: []
--- !u!114 &1382893872
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1382893869}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1382893873}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1382893873
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1382893869}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1382893874
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1382893869}
--- !u!1 &1392591612
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1392591613}
  - component: {fileID: 1392591617}
  - component: {fileID: 1392591616}
  - component: {fileID: 1392591615}
  - component: {fileID: 1392591614}
  m_Layer: 0
  m_Name: GridWithSubcategoriesButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1392591613
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1392591612}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2046215531}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1392591614
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1392591612}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: grid_with_subcategories
  allSceneNamesInOrder: []
--- !u!114 &1392591615
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1392591612}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1392591616}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1392591616
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1392591612}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1392591617
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1392591612}
--- !u!1 &1408686042
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1408686043}
  - component: {fileID: 1408686045}
  - component: {fileID: 1408686044}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1408686043
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1408686042}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1846914042}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1408686044
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1408686042}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: 'Item drag to reorder +

    move between lists'
--- !u!222 &1408686045
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1408686042}
--- !u!1 &1470224314
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1470224315}
  - component: {fileID: 1470224318}
  - component: {fileID: 1470224317}
  - component: {fileID: 1470224316}
  m_Layer: 5
  m_Name: Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1470224315
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1470224314}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.9999999}
  m_Children: []
  m_Father: {fileID: 1303894993}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1470224316
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1470224314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1254083943, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AspectMode: 4
  m_AspectRatio: 1.26
--- !u!114 &1470224317
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1470224314}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 21300000, guid: fada04147642d444192a2443452fb50d, type: 3}
  m_Type: 0
  m_PreserveAspect: 1
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1470224318
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1470224314}
--- !u!1 &1482459722
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1482459723}
  - component: {fileID: 1482459727}
  - component: {fileID: 1482459726}
  - component: {fileID: 1482459725}
  - component: {fileID: 1482459724}
  m_Layer: 0
  m_Name: CSFButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1482459723
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1482459722}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 991845380}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 19
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1482459724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1482459722}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: content_size_fitter
  allSceneNamesInOrder: []
--- !u!114 &1482459725
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1482459722}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1482459726}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1482459726
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1482459722}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1482459727
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1482459722}
--- !u!1 &1488514220
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1488514221}
  - component: {fileID: 1488514225}
  - component: {fileID: 1488514224}
  - component: {fileID: 1488514223}
  - component: {fileID: 1488514222}
  m_Layer: 0
  m_Name: NestedScrollViewsButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1488514221
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1488514220}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 213964104}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1488514222
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1488514220}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: nested_scrollviews
  allSceneNamesInOrder: []
--- !u!114 &1488514223
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1488514220}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1488514224}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1488514224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1488514220}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1488514225
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1488514220}
--- !u!1 &1504101600
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1504101601}
  - component: {fileID: 1504101603}
  - component: {fileID: 1504101602}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1504101601
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1504101600}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2121605940}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1504101602
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1504101600}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Select & delete in Grid
--- !u!222 &1504101603
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1504101600}
--- !u!1 &1516315084
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1516315085}
  - component: {fileID: 1516315089}
  - component: {fileID: 1516315088}
  - component: {fileID: 1516315087}
  - component: {fileID: 1516315086}
  m_Layer: 0
  m_Name: MainExampleButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1516315085
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1516315084}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 291180267}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1516315086
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1516315084}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: example
  allSceneNamesInOrder: []
--- !u!114 &1516315087
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1516315084}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1516315088}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1516315088
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1516315084}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1516315089
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1516315084}
--- !u!1 &1542415219
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1542415220}
  - component: {fileID: 1542415222}
  - component: {fileID: 1542415221}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1542415220
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1542415219}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 358998812}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1542415221
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1542415219}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Hierarchy + sticky headers
--- !u!222 &1542415222
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1542415219}
--- !u!1 &1568887590
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1568887591}
  - component: {fileID: 1568887594}
  - component: {fileID: 1568887593}
  - component: {fileID: 1568887592}
  - component: {fileID: 1568887595}
  m_Layer: 5
  m_Name: ButtonsPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1568887591
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1568887590}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.95572597, y: 0.9557254, z: 0.9557254}
  m_Children:
  - {fileID: 1828121640}
  - {fileID: 1516315085}
  - {fileID: 1730056692}
  - {fileID: 1488514221}
  - {fileID: 919778732}
  - {fileID: 1732247403}
  - {fileID: 1392591613}
  - {fileID: 1846914042}
  - {fileID: 482446899}
  - {fileID: 358998812}
  - {fileID: 762738917}
  - {fileID: 1644985581}
  - {fileID: 2026738394}
  - {fileID: 228830447}
  - {fileID: 2121605940}
  - {fileID: 1001532910}
  - {fileID: 1214215661}
  - {fileID: 1361331596}
  - {fileID: 369320400}
  - {fileID: 1482459723}
  - {fileID: 1382893870}
  - {fileID: 885488803}
  - {fileID: 1198710708}
  - {fileID: 896607774}
  - {fileID: 826429639}
  - {fileID: 1909775993}
  m_Father: {fileID: 1303894993}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.15200001, y: 0.80514264}
  m_AnchorMax: {x: 0.8594689, y: 0.80514264}
  m_AnchoredPosition: {x: -0.06796265, y: 0.4095459}
  m_SizeDelta: {x: 236, y: 0}
  m_Pivot: {x: 0.49999994, y: 1}
--- !u!114 &1568887592
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1568887590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -2095666955, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 10
    m_Right: 10
    m_Top: 10
    m_Bottom: 10
  m_ChildAlignment: 4
  m_StartCorner: 0
  m_StartAxis: 0
  m_CellSize: {x: 287, y: 46.8}
  m_Spacing: {x: 16.19, y: 5}
  m_Constraint: 0
  m_ConstraintCount: 2
--- !u!114 &1568887593
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1568887590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.084}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1568887594
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1568887590}
--- !u!114 &1568887595
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1568887590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1741964061, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 0
  m_VerticalFit: 2
--- !u!1 &1578989383
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1578989384}
  - component: {fileID: 1578989386}
  - component: {fileID: 1578989385}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1578989384
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1578989383}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1382893870}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1578989385
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1578989383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Expand item to variable size
--- !u!222 &1578989386
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1578989383}
--- !u!224 &1630309479 stripped
RectTransform:
  m_PrefabParentObject: {fileID: 22450422, guid: 9ef2faede4b04a946b28eef8a8ab244c,
    type: 2}
  m_PrefabInternal: {fileID: 663717106}
--- !u!1 &1640136424
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1640136425}
  - component: {fileID: 1640136427}
  - component: {fileID: 1640136426}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1640136425
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1640136424}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 919778732}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1640136426
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1640136424}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: "Grid with variable \ncolumns/rows + Async"
--- !u!222 &1640136427
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1640136424}
--- !u!1 &1644985580
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1644985581}
  - component: {fileID: 1644985585}
  - component: {fileID: 1644985584}
  - component: {fileID: 1644985583}
  - component: {fileID: 1644985582}
  m_Layer: 0
  m_Name: MultiPrefabsButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1644985581
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1644985580}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 461957007}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1644985582
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1644985580}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: multiple_prefabs
  allSceneNamesInOrder: []
--- !u!114 &1644985583
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1644985580}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1644985584}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1644985584
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1644985580}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1644985585
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1644985580}
--- !u!1 &1730056691
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1730056692}
  - component: {fileID: 1730056696}
  - component: {fileID: 1730056695}
  - component: {fileID: 1730056694}
  - component: {fileID: 1730056693}
  m_Layer: 0
  m_Name: TableViewButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1730056692
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1730056691}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 433294952}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1730056693
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1730056691}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: table_view
  allSceneNamesInOrder: []
--- !u!114 &1730056694
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1730056691}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1730056695}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1730056695
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1730056691}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1730056696
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1730056691}
--- !u!1 &1732247402
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1732247403}
  - component: {fileID: 1732247407}
  - component: {fileID: 1732247406}
  - component: {fileID: 1732247405}
  - component: {fileID: 1732247404}
  m_Layer: 0
  m_Name: PackedGridButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1732247403
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1732247402}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 540795019}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1732247404
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1732247402}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: grid_packed
  allSceneNamesInOrder: []
--- !u!114 &1732247405
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1732247402}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1732247406}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1732247406
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1732247402}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1732247407
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1732247402}
--- !u!1 &1828121639
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1828121640}
  - component: {fileID: 1828121644}
  - component: {fileID: 1828121643}
  - component: {fileID: 1828121642}
  - component: {fileID: 1828121641}
  m_Layer: 0
  m_Name: DateTimePickerButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1828121640
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1828121639}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_Children:
  - {fileID: 2111000360}
  - {fileID: 689793890}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1828121641
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1828121639}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: datetime_picker
  allSceneNamesInOrder: []
--- !u!114 &1828121642
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1828121639}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1828121643}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1828121643
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1828121639}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1828121644
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1828121639}
--- !u!1 &1846914041
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1846914042}
  - component: {fileID: 1846914046}
  - component: {fileID: 1846914045}
  - component: {fileID: 1846914044}
  - component: {fileID: 1846914043}
  m_Layer: 0
  m_Name: ItemDragging
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1846914042
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1846914041}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1408686043}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1846914043
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1846914041}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: item_dragging
  allSceneNamesInOrder: []
--- !u!114 &1846914044
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1846914041}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1846914045}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1846914045
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1846914041}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1846914046
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1846914041}
--- !u!1 &1855402675
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1855402676}
  - component: {fileID: 1855402678}
  - component: {fileID: 1855402677}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1855402676
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1855402675}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 885488803}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1855402677
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1855402675}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: "Different prefab per \norientation"
--- !u!222 &1855402678
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1855402675}
--- !u!1 &1875792097
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1875792098}
  - component: {fileID: 1875792100}
  - component: {fileID: 1875792099}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1875792098
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1875792097}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1361331596}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1875792099
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1875792097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Pull to refresh or load more
--- !u!222 &1875792100
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1875792097}
--- !u!1 &1909775992
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1909775993}
  - component: {fileID: 1909775997}
  - component: {fileID: 1909775996}
  - component: {fileID: 1909775995}
  - component: {fileID: 1909775994}
  m_Layer: 0
  m_Name: Lite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &1909775993
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1909775992}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1109325038}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 25
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 463.98132, y: -447.8}
  m_SizeDelta: {x: 287, y: 46.8}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1909775994
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1909775992}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: lite_version_drag_and_drop
  allSceneNamesInOrder: []
--- !u!114 &1909775995
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1909775992}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 0
  m_TargetGraphic: {fileID: 1909775996}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &1909775996
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1909775992}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &1909775997
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1909775992}
--- !u!1 &1940838230
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 1940838231}
  - component: {fileID: 1940838233}
  - component: {fileID: 1940838232}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1940838231
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1940838230}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 369320400}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1940838232
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1940838230}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Chat
--- !u!222 &1940838233
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1940838230}
--- !u!1 &2026738393
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2026738394}
  - component: {fileID: 2026738398}
  - component: {fileID: 2026738397}
  - component: {fileID: 2026738396}
  - component: {fileID: 2026738395}
  m_Layer: 0
  m_Name: PageView
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2026738394
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2026738393}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1097883689}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2026738395
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2026738393}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: page_view_worldspace
  allSceneNamesInOrder: []
--- !u!114 &2026738396
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2026738393}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 2026738397}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &2026738397
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2026738393}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &2026738398
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2026738393}
--- !u!1 &2046215530
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2046215531}
  - component: {fileID: 2046215533}
  - component: {fileID: 2046215532}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2046215531
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2046215530}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1392591613}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.9565002, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2046215532
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2046215530}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Grid with subcategories
--- !u!222 &2046215533
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2046215530}
--- !u!1 &2111000359
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128318, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2111000360}
  - component: {fileID: 2111000362}
  - component: {fileID: 2111000361}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2111000360
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2111000359}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1828121640}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.058499873, y: 0}
  m_AnchorMax: {x: 0.6599746, y: 1}
  m_AnchoredPosition: {x: -1, y: 0}
  m_SizeDelta: {x: -0.29999924, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2111000361
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2111000359}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 708705254, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_FontData:
    m_Font: {fileID: 12800000, guid: 9e5f8fcfcbfe4494d9c37a8825204428, type: 3}
    m_FontSize: 16
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 0
    m_HorizontalOverflow: 1
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: DateTime picker
--- !u!222 &2111000362
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2111000359}
--- !u!1 &2121605939
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 128316, guid: ebffa6b69239bfd4384a12b709983ed7, type: 2}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 5
  m_Component:
  - component: {fileID: 2121605940}
  - component: {fileID: 2121605944}
  - component: {fileID: 2121605943}
  - component: {fileID: 2121605942}
  - component: {fileID: 2121605941}
  m_Layer: 0
  m_Name: SelectAndDeleteGrid
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2121605940
RectTransform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22428316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2121605939}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1504101601}
  m_Father: {fileID: 1568887591}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2121605941
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428318, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2121605939}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2ee060e808063d949b5e29f00bf828b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  loadMode: 2
  sceneName: select_and_delete
  allSceneNamesInOrder: []
--- !u!114 &2121605942
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428312, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2121605939}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1392445389, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 2121605943}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.Button+ButtonClickedEvent, UnityEngine.UI, Version=*******,
      Culture=neutral, PublicKeyToken=null
--- !u!114 &2121605943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 11428314, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2121605939}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -*********, guid: f5f67c52d1564df4a8936ccd202a3bd8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.85882354, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
    m_TypeName: UnityEngine.UI.MaskableGraphic+CullStateChangedEvent, UnityEngine.UI,
      Version=*******, Culture=neutral, PublicKeyToken=null
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
--- !u!222 &2121605944
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 22228316, guid: ebffa6b69239bfd4384a12b709983ed7,
    type: 2}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2121605939}
