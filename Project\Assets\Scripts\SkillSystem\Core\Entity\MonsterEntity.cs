﻿using UnityEngine;
using SkillSystem.Core;
using Cysharp.Threading.Tasks;

public class MonsterEntity : Entity, IDamageable, ITargetable
{
    public MonsterEntity()
    {
        // 设置实体类型为怪物
        EntityType = EntityType.Monster;

        // 初始化AI组件
        aiComponent = new MonsterAIComponent();
    }

    [Header("怪物特有属性")]
    public float health = 100f;        // 血量
    public float maxHealth = 100f;     // 最大血量
    public float attackRange = 3f;     // 攻击范围
    public MonsterType monsterType = MonsterType.Normal; // 怪物类型

    [Header("AI组件")]
    private MonsterAIComponent aiComponent;  // AI组件
    
    public enum MonsterType
    {
        Normal,     // 普通怪物
        Elite,      // 精英怪物
        Boss,       // Boss怪物
        Minion      // 小怪
    }
    
    #region IDamageable 接口实现
    
    /// <summary>
    /// 是否存活
    /// </summary>
    public bool IsAlive => health > 0;
    
    /// <summary>
    /// 受到伤害 - IDamageable接口实现
    /// </summary>
    public void TakeDamage(DamageInfo damageInfo)
    {
        if (!IsAlive) return;
        
        float finalDamage = damageInfo.CalculateFinalDamage();
        TakeDamage(finalDamage);
        
        Debug.Log($"[MonsterEntity {UniqueId}] 受到技能伤害: {finalDamage} 来自技能: {damageInfo.SkillName}");
    }
    
    #endregion
    
    #region ITargetable 接口实现
    
    /// <summary>
    /// 目标关系 - 怪物默认为敌方
    /// </summary>
    public TargetRelation Relation => TargetRelation.Enemy;
    
    #endregion
    
    /// <summary>
    /// 受到伤害 - 原始方法
    /// </summary>
    public void TakeDamage(float damage)
    {
        if (!IsAlive) return;
        
        health -= damage;
        health = Mathf.Max(0f, health);
        
        Debug.Log($"[MonsterEntity {UniqueId}] 受到伤害: {damage}, 剩余血量: {health}");
        
        if (health <= 0)
        {
            OnDeath();
        }
    }
    
    /// <summary>
    /// 死亡处理
    /// </summary>
    protected virtual void OnDeath()
    {
        Debug.Log($"[MonsterEntity {UniqueId}] 死亡");
        
        // 这里可以添加死亡特效、掉落物品等逻辑
        
        // 通知EntityManager清理
        if (entityObject != null)
        {
            // 可以添加死亡特效和延迟销毁
            entityObject.GetComponent<Animator>().SetTrigger("Die");
            entityObject.GetComponent<MonsterAI>().enabled = false;
            Object.Destroy(entityObject, 2f);
        }
    }
    
    /// <summary>
    /// 获取当前血量百分比
    /// </summary>
    public float GetHealthPercentage()
    {
        return maxHealth > 0 ? health / maxHealth : 0f;
    }
    
    /// <summary>
    /// 更新怪物状态
    /// </summary>
    public override void Update(float deltaTime)
    {
        // 调用基类的Update（包含自动技能释放逻辑）
        base.Update(deltaTime);

        // 更新AI组件
        aiComponent?.UpdateAI(deltaTime);

        // 怪物特有的更新逻辑
        UpdateMonsterStatus(deltaTime);
    }
    
    /// <summary>
    /// 更新怪物特有状态
    /// </summary>
    private void UpdateMonsterStatus(float deltaTime)
    {
        // 怪物的特殊行为更新
        // 例如：特殊技能冷却、状态效果等
        // AI行为已经由aiComponent处理

        // 示例：简单的血量回复（如果需要）
        // if (IsAlive && health < maxHealth)
        // {
        //     RestoreHealth(healthRegenRate * deltaTime);
        // }
    }

    #region AI相关接口

    /// <summary>
    /// 重写初始化方法，添加AI组件初始化
    /// </summary>
    public override async UniTask Initialize(int entityCfgId, long uniqueId = 0)
    {
        // 调用基类初始化
        await base.Initialize(entityCfgId, uniqueId);

        // 初始化AI组件
        InitializeAI();

        // 设置自动技能释放（怪物默认启用）
        SetAutoSkillReleaseSettings(
            enable: true,
            interval: 2f,
            autoTarget: true,
            targetRange: 15f,
            oneSkillPerInterval: true,
            useRotation: true
        );

        // 注册到MonsterManager
        MonsterManager.Instance.RegisterMonster(this);

        Debug.Log($"[MonsterEntity {UniqueId}] 怪物初始化完成，AI已启用");
    }

    /// <summary>
    /// 初始化AI组件
    /// </summary>
    private void InitializeAI()
    {
        if (aiComponent != null)
        {
            aiComponent.Initialize(this);

            // 配置AI参数
            aiComponent.detectionRange = 15f;
            aiComponent.attackRange = attackRange;
            aiComponent.chaseRange = 20f;
            aiComponent.moveSpeed = 3f;
            aiComponent.skillReleaseInterval = 2f;
            aiComponent.enableAutoSkill = true;
            aiComponent.usePathfinding = true;
        }
    }

    /// <summary>
    /// 获取AI组件
    /// </summary>
    public MonsterAIComponent GetAIComponent()
    {
        return aiComponent;
    }

    /// <summary>
    /// 设置AI启用状态
    /// </summary>
    public void SetAIEnabled(bool enabled)
    {
        aiComponent?.SetAIEnabled(enabled);
    }

    /// <summary>
    /// 强制设置AI目标
    /// </summary>
    public void SetAITarget(Entity target)
    {
        aiComponent?.ForceSetTarget(target);
    }

    /// <summary>
    /// 获取AI状态信息
    /// </summary>
    public string GetAIStatus()
    {
        return aiComponent?.GetAIStatusInfo() ?? "AI组件未初始化";
    }

    #endregion

    #region 重写死亡处理

    /// <summary>
    /// 死亡处理 - 添加AI清理
    /// </summary>
    protected override void OnDeath()
    {
        Debug.Log($"[MonsterEntity {UniqueId}] 死亡");

        // 禁用AI
        aiComponent?.SetAIEnabled(false);

        // 这里可以添加死亡特效、掉落物品等逻辑

        // 通知EntityManager清理
        if (entityObject != null)
        {
            // 可以添加死亡特效和延迟销毁
            var animator = entityObject.GetComponent<Animator>();
            if (animator != null)
            {
                animator.SetTrigger("Die");
            }

            // 禁用旧的MonsterAI组件（如果存在）
            var oldMonsterAI = entityObject.GetComponent<MonsterAI>();
            if (oldMonsterAI != null)
            {
                oldMonsterAI.enabled = false;
            }

            Object.Destroy(entityObject, 2f);
        }

        // 从MonsterManager注销
        MonsterManager.Instance.UnregisterMonster(this);

        // 清理AI组件
        aiComponent?.Dispose();
    }

    #endregion
}
