using cfg.Bean;
using UnityEngine;
using SkillSystem.Core;

public class MonsterAI : MonoBehaviour
{
    #region 基础属性
    
    public int MonsterId { get; set; }
    public int CurrentWaveId { get; set; }
    
    [Header("基础属性")]
    public float detectionRange = 10f;
    public float attackRange = 2f;
    public float moveSpeed = 10f;
    public int attackDamage = 10;
    public float attackCooldown = 2f;
    
    [Header("状态")]
    public bool isDead = false;
    public bool isInitialized = false;
    
    #endregion
    
    #region 组件引用
    
    public Transform target;
    private Animator animator;
    private float attackTimer = 0f;
    private Monster_MonsterAttr currentConfig;
    
    #endregion
    
    #region 生命周期
    
    private void Start()
    {
        animator = GetComponent<Animator>();
        
        // 如果还没有初始化，尝试从配置表初始化
        if (!isInitialized && MonsterId > 0)
        {
            var config = TableManager.Instance.ConfigTables.Monster_MonsterAttrModel.Get(MonsterId);
            if (config != null)
            {
                Initialize(config);
            }
        }
    }
    
    private void OnDestroy()
    {
    }

    private void Update()
    {
        if (isDead || !isInitialized) return;

        attackTimer += Time.deltaTime;

        // 锁定目标逻辑
        if (target == null)
        {
            FindTarget();
        }
        else
        {
            // 检查目标是否仍然有效
            if (target == null || !target.gameObject.activeInHierarchy)
            {
                FindTarget();
                return;
            }
            
            // 计算与目标的距离
            float distance = Vector3.Distance(transform.position, target.position);

            // 攻击逻辑
            if (distance <= attackRange && attackTimer >= attackCooldown)
            {
                Attack();
                attackTimer = 0f;
            }
            // 移动逻辑
            else if (distance > attackRange)
            {
                MoveTowardsTarget();
            }
            else
            {
                // 在攻击范围内但还在冷却中
                StopMoving();
            }
        }
    }
    
    #endregion
    
    #region 初始化和配置
    
    /// <summary>
    /// 初始化怪物属性
    /// </summary>
    /// <param name="config">怪物配置</param>
    public void Initialize(Monster_MonsterAttr config)
    {
        if (config == null)
        {
            Debug.LogError($"[MonsterAI] 初始化失败，配置为空 ID:{MonsterId}");
            return;
        }
        
        currentConfig = config;
        MonsterId = config.id;
        
        // 应用配置属性
        ApplyConfig(config);
        
        // 重置状态
        ResetState();
        
        isInitialized = true;
        
        Debug.Log($"[MonsterAI] 初始化完成 ID:{MonsterId}, Name:{config.displayName}");
    }
    
    /// <summary>
    /// 应用配置属性
    /// </summary>
    /// <param name="config">怪物配置</param>
    private void ApplyConfig(Monster_MonsterAttr config)
    {
        moveSpeed = config.moveSpeed;
        attackDamage = config.damage;
        attackRange = config.attackRange;
        attackCooldown = 1f / config.attackSpeed; // 攻击间隔 = 1 / 攻击速度
        
        // 可以根据配置调整其他属性
        detectionRange = attackRange * 2f; // 检测范围为攻击范围的两倍
    }
    
    /// <summary>
    /// 重置怪物状态
    /// </summary>
    public void ResetState()
    {
        isDead = false;
        target = null;
        attackTimer = 0f;
        
        // 重置动画状态
        if (animator != null)
        {
            animator.SetBool("IsMoving", false);
            animator.SetBool("IsDead", false);
            animator.ResetTrigger("Attack");
        }
        
        // 重置碰撞器
        var collider = GetComponent<Collider>();
        if (collider != null)
        {
            collider.enabled = true;
        }
        
        // 重置脚本启用状态
        this.enabled = true;
    }
    
    #endregion
    
    #region 目标和移动
    
    /// <summary>
    /// 寻找目标
    /// </summary>
    private void FindTarget()
    {
        // 通过EntityManager获取Hero实体
        var heroEntities = EntityManager.Instance.GetEntitiesByType(EntityType.Hero);
        if (heroEntities.Count == 0) return;
        
        var heroEntity = heroEntities[0];
        if (heroEntity?.entityObject == null) return;
        
        var heroTransform = heroEntity.entityObject.transform;
        target = heroTransform;
        //float distance = Vector3.Distance(transform.position, heroTransform.position);
        TriggerTargetChangedEvent(target.gameObject);
        // if (distance <= detectionRange)
        // {
        //     target = heroTransform;
        //     
        //     // 触发目标改变事件
        //     TriggerTargetChangedEvent(target.gameObject);
        // }
    }

    /// <summary>
    /// 向目标移动
    /// </summary>
    private void MoveTowardsTarget()
    {
        if (target == null) return;

        Vector3 direction = (target.position - transform.position).normalized;
        transform.position += direction * moveSpeed * Time.deltaTime;
        
        // 面向目标
        transform.LookAt(target);

        // 更新动画
        if (animator != null)
        {
            animator.SetBool("IsMoving", true);
        }
    }
    
    /// <summary>
    /// 停止移动
    /// </summary>
    private void StopMoving()
    {
        if (animator != null)
        {
            animator.SetBool("IsMoving", false);
        }
    }

    #endregion
    
    #region 攻击和伤害
    
    /// <summary>
    /// 攻击目标
    /// </summary>
    private void Attack()
    {
        // if (target == null || isDead) return;

        // // 更新动画
        // if (animator != null)
        // {
        //     animator.SetTrigger("Attack");
        // }

        // // 应用伤害 - 使用新的伤害系统
        // var targetCombat = target.GetComponent<SkillSystem.Core.UnifiedCharacterCombat>();
        // if (targetCombat != null && targetCombat.IsAlive)
        // {
        //     // 创建伤害信息
        //     var damageInfo = new SkillSystem.Core.DamageInfo(gameObject, attackDamage, SkillSystem.Core.DamageType.Physical)
        //     {
        //         Target = target.gameObject,
        //         HitPoint = target.position + Vector3.up,
        //         HitDirection = (target.position - transform.position).normalized,
        //         SkillName = "怪物攻击"
        //     };
            
        //     // 应用伤害
        //     targetCombat.TakeDamage(damageInfo);

        //     if(target == null) return;
            
        //     // 触发伤害事件
        //     TriggerDamageDealtEvent(target.gameObject, attackDamage);
        // }
        // else
        // {
        //     Debug.LogWarning($"[MonsterAI] 目标 {target.name} 没有UnifiedCharacterCombat组件，无法造成伤害");
        // }
    }

    /// <summary>
    /// 死亡处理（由UnifiedCharacterCombat调用）
    /// </summary>
    public void OnDeath()
    {
        if (isDead) return;
        
        isDead = true;
        
        // 停止移动
        StopMoving();
        
        // 禁用AI行为
        this.enabled = false;

        
        Debug.Log($"[MonsterAI] 怪物死亡 ID:{MonsterId}");
    }
    
    #endregion
    
    #region 事件触发
    
    /// <summary>
    /// 触发目标改变事件
    /// </summary>
    /// <param name="newTarget">新目标</param>
    private void TriggerTargetChangedEvent(GameObject newTarget)
    {
        // 这里可以触发目标改变事件
        Debug.Log($"[MonsterAI] 目标改变 ID:{MonsterId}, 目标:{newTarget?.name}");
    }
    
    /// <summary>
    /// 触发伤害造成事件
    /// </summary>
    /// <param name="target">目标</param>
    /// <param name="damage">伤害值</param>
    private void TriggerDamageDealtEvent(GameObject target, int damage)
    {
        // 这里可以触发伤害造成事件
        Debug.Log($"[MonsterAI] 造成伤害 ID:{MonsterId}, 目标:{target?.name}, 伤害:{damage}");
    }
    
    /// <summary>
    /// 触发受到伤害事件
    /// </summary>
    /// <param name="attacker">攻击者</param>
    /// <param name="damage">伤害值</param>
    private void TriggerDamageReceivedEvent(GameObject attacker, int damage)
    {
        Debug.Log($"[MonsterAI] 受到伤害 ID:{MonsterId}, 攻击者:{attacker?.name}, 伤害:{damage}");
        // 这里可以触发受到伤害事件
        // EventManager.Instance.Trigger(EventID.MonsterEvent_DamageReceived, eventParams);
    }
    
    /// <summary>
    /// 触发死亡事件
    /// </summary>
    /// <param name="killer">击杀者</param>
    /// <param name="finalDamage">最终伤害</param>
    private void TriggerDeathEvent(GameObject killer, float finalDamage)
    {
        Debug.Log($"[MonsterAI] 死亡事件 ID:{MonsterId}, 击杀者:{killer?.name}, 伤害:{finalDamage}");
        
        // 延迟销毁 - 这里将被对象池管理器接管
        Destroy(gameObject, 3f);
    }
    
    #endregion
    
    #region 调试信息
    
    /// <summary>
    /// 获取怪物状态信息
    /// </summary>
    /// <returns>状态信息字符串</returns>
    public string GetStatusInfo()
    {
        if (!isInitialized) return "未初始化";
        
        string targetName = target != null ? target.name : "无目标";
        string status = isDead ? "死亡" : "活跃";
        
        return $"ID:{MonsterId}, 状态:{status}, 目标:{targetName}, 波次:{CurrentWaveId}";
    }
    
    /// <summary>
    /// 在编辑器中显示调试信息
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        if (!Application.isPlaying) return;
        
        // 绘制检测范围
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
        
        // 绘制攻击范围
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
        
        // 绘制到目标的连线
        if (target != null)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawLine(transform.position, target.position);
        }
    }
    
    #endregion
}    