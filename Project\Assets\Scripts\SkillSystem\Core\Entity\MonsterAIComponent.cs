using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using Pathfinding;

namespace SkillSystem.Core
{
    /// <summary>
    /// 怪物AI核心组件 - 不依赖MonoBehaviour的AI系统
    /// 负责怪物的自动锁敌、移动控制、技能释放等核心AI功能
    /// </summary>
    public class MonsterAIComponent
    {
        #region 配置参数
        
        [Header("AI基础设置")]
        public bool enableAI = true;                    // 是否启用AI
        public float updateInterval = 0.1f;             // AI更新间隔
        public float detectionRange = 15f;              // 检测范围
        public float attackRange = 3f;                  // 攻击范围
        public float chaseRange = 20f;                  // 追击范围
        public float loseTargetTime = 5f;               // 失去目标后的等待时间
        
        [Header("移动设置")]
        public float moveSpeed = 3f;                    // 移动速度
        public float rotationSpeed = 180f;              // 旋转速度
        public float stopDistance = 1.5f;               // 停止距离
        public bool usePathfinding = true;              // 是否使用寻路
        
        [Header("技能释放设置")]
        public bool enableAutoSkill = true;             // 是否启用自动技能释放
        public float skillReleaseInterval = 2f;         // 技能释放间隔
        public bool prioritizeCloseTargets = true;      // 是否优先攻击近距离目标
        
        #endregion
        
        #region 内部状态
        
        public MonsterEntity owner;                    // 拥有者（改为public供状态机访问）
        private Entity currentTarget;                   // 当前目标
        private Vector3 lastKnownTargetPosition;        // 目标最后已知位置
        private MonsterAIStateMachine stateMachine;     // AI状态机
        private float lastUpdateTime;                   // 上次更新时间
        private float lastSkillReleaseTime;             // 上次技能释放时间
        private float targetLostTime;                   // 失去目标的时间
        private bool isMoving;                          // 是否正在移动

        // 寻路相关
        private IAstarAI pathfindingAgent;              // 寻路代理
        private bool hasReachedDestination;             // 是否到达目的地
        
        #endregion
        
        #region AI状态枚举（保留兼容性）

        public enum MonsterAIState
        {
            Idle,           // 空闲状态
            Patrol,         // 巡逻状态
            Searching,      // 搜索状态
            Chasing,        // 追击状态
            Attacking,      // 攻击状态
            Returning,      // 返回状态
            Dead            // 死亡状态
        }

        #endregion
        
        #region 初始化和销毁
        
        /// <summary>
        /// 初始化AI组件
        /// </summary>
        public void Initialize(MonsterEntity monsterEntity)
        {
            owner = monsterEntity;
            lastUpdateTime = Time.time;
            lastSkillReleaseTime = Time.time;

            // 初始化状态机
            stateMachine = new MonsterAIStateMachine();
            stateMachine.Initialize(this);

            // 初始化寻路组件
            InitializePathfinding();

            Debug.Log($"[MonsterAI {owner.UniqueId}] AI组件初始化完成");
        }
        
        /// <summary>
        /// 初始化寻路组件
        /// </summary>
        private void InitializePathfinding()
        {
            if (!usePathfinding || owner?.entityObject == null) return;

            // 检查A*系统是否可用
            if (!PathfindingHelper.IsAstarSystemAvailable())
            {
                Debug.LogWarning($"[MonsterAI {owner.UniqueId}] A*寻路系统不可用，禁用寻路功能");
                usePathfinding = false;
                return;
            }

            // 使用PathfindingHelper配置寻路组件
            var followerEntity = PathfindingHelper.ConfigureFollowerEntity(owner.entityObject, this);
            if (followerEntity != null)
            {
                pathfindingAgent = followerEntity;
                Debug.Log($"[MonsterAI {owner.UniqueId}] 寻路组件初始化完成");
            }
            else
            {
                Debug.LogWarning($"[MonsterAI {owner.UniqueId}] 寻路组件初始化失败");
                usePathfinding = false;
            }
        }
        
        /// <summary>
        /// 销毁AI组件
        /// </summary>
        public void Dispose()
        {
            currentTarget = null;
            pathfindingAgent = null;

            // 销毁状态机
            stateMachine?.Dispose();
            stateMachine = null;

            Debug.Log($"[MonsterAI {owner.UniqueId}] AI组件已销毁");
        }
        
        #endregion
        
        #region 主要更新逻辑
        
        /// <summary>
        /// 更新AI逻辑
        /// </summary>
        public void UpdateAI(float deltaTime)
        {
            if (!enableAI || owner == null || !owner.IsAlive)
            {
                if (stateMachine != null && !stateMachine.IsInState(MonsterAIStateMachine.AIState.Dead))
                {
                    stateMachine.ChangeState(MonsterAIStateMachine.AIState.Dead);
                }
                return;
            }

            // 检查更新间隔
            if (Time.time - lastUpdateTime < updateInterval) return;
            lastUpdateTime = Time.time;

            // 更新AI状态机
            stateMachine?.UpdateStateMachine(deltaTime);

            // 根据当前状态执行相应的行为
            ExecuteCurrentStateBehavior(deltaTime);

            // 更新技能释放
            UpdateSkillRelease(deltaTime);
        }
        
        /// <summary>
        /// 执行当前状态的行为
        /// </summary>
        private void ExecuteCurrentStateBehavior(float deltaTime)
        {
            if (stateMachine == null) return;

            var currentState = stateMachine.GetCurrentState();

            switch (currentState)
            {
                case MonsterAIStateMachine.AIState.Idle:
                    ExecuteIdleBehavior();
                    break;

                case MonsterAIStateMachine.AIState.Patrol:
                    ExecutePatrolBehavior();
                    break;

                case MonsterAIStateMachine.AIState.Searching:
                    ExecuteSearchingBehavior();
                    break;

                case MonsterAIStateMachine.AIState.Chasing:
                    ExecuteChasingBehavior();
                    break;

                case MonsterAIStateMachine.AIState.Attacking:
                    ExecuteAttackingBehavior();
                    break;

                case MonsterAIStateMachine.AIState.Returning:
                    ExecuteReturningBehavior();
                    break;

                case MonsterAIStateMachine.AIState.Stunned:
                    ExecuteStunnedBehavior();
                    break;

                case MonsterAIStateMachine.AIState.Dead:
                    ExecuteDeadBehavior();
                    break;
            }
        }
        
        #endregion
        
        #region 状态行为实现

        /// <summary>
        /// 执行空闲行为
        /// </summary>
        private void ExecuteIdleBehavior()
        {
            // 搜索目标
            var target = FindNearestTarget();
            if (target != null)
            {
                SetTarget(target);
                stateMachine?.ChangeState(MonsterAIStateMachine.AIState.Chasing);
                return;
            }

            // 停止移动
            StopMovement();
        }

        /// <summary>
        /// 执行巡逻行为
        /// </summary>
        private void ExecutePatrolBehavior()
        {
            // 搜索目标
            var target = FindNearestTarget();
            if (target != null)
            {
                SetTarget(target);
                stateMachine?.ChangeState(MonsterAIStateMachine.AIState.Chasing);
                return;
            }

            // 巡逻逻辑由状态机内部处理
            // 这里只需要执行移动
            // 具体的巡逻目标选择在PatrolState中处理
        }

        /// <summary>
        /// 执行搜索行为
        /// </summary>
        private void ExecuteSearchingBehavior()
        {
            // 搜索目标
            var target = FindNearestTarget();
            if (target != null)
            {
                SetTarget(target);
                stateMachine?.ChangeState(MonsterAIStateMachine.AIState.Chasing);
                return;
            }

            // 在最后已知位置附近移动
            if (lastKnownTargetPosition != Vector3.zero)
            {
                MoveToTarget(lastKnownTargetPosition);
            }
        }

        /// <summary>
        /// 执行追击行为
        /// </summary>
        private void ExecuteChasingBehavior()
        {
            if (currentTarget == null || !currentTarget.IsAlive)
            {
                LoseTarget();
                return;
            }

            // 移动向目标
            MoveToTarget(currentTarget.Position);

            // 面向目标
            LookAtTarget(currentTarget.Position);
        }

        /// <summary>
        /// 执行攻击行为
        /// </summary>
        private void ExecuteAttackingBehavior()
        {
            if (currentTarget == null || !currentTarget.IsAlive)
            {
                LoseTarget();
                return;
            }

            // 停止移动，面向目标
            StopMovement();
            LookAtTarget(currentTarget.Position);

            // 技能释放在UpdateSkillRelease中处理
        }

        /// <summary>
        /// 执行返回行为
        /// </summary>
        private void ExecuteReturningBehavior()
        {
            // 返回逻辑由状态机内部处理
            // 这里只需要执行移动
        }

        /// <summary>
        /// 执行眩晕行为
        /// </summary>
        private void ExecuteStunnedBehavior()
        {
            // 停止所有行动
            StopMovement();
        }

        /// <summary>
        /// 执行死亡行为
        /// </summary>
        private void ExecuteDeadBehavior()
        {
            // 停止所有行动
            StopMovement();
        }

        #endregion

        #region 目标管理

        /// <summary>
        /// 寻找最近的目标
        /// </summary>
        private Entity FindNearestTarget()
        {
            if (EntityManager.Instance == null) return null;

            Entity nearestTarget = null;
            float nearestDistance = float.MaxValue;

            var allEntities = EntityManager.Instance.GetAllEntities();

            foreach (var entity in allEntities)
            {
                if (entity == null || entity == owner) continue;

                // 使用Entity的敌对关系逻辑
                if (!owner.IsValidTarget(entity)) continue;

                float distance = Vector3.Distance(owner.Position, entity.Position);
                if (distance <= detectionRange && distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestTarget = entity;
                }
            }

            return nearestTarget;
        }

        /// <summary>
        /// 设置目标
        /// </summary>
        private void SetTarget(Entity target)
        {
            currentTarget = target;
            lastKnownTargetPosition = target.Position;
            targetLostTime = 0f;

            Debug.Log($"[MonsterAI {owner.UniqueId}] 锁定目标: {target.UniqueId}");
        }

        /// <summary>
        /// 失去目标
        /// </summary>
        private void LoseTarget()
        {
            if (currentTarget != null)
            {
                Debug.Log($"[MonsterAI {owner.UniqueId}] 失去目标: {currentTarget.UniqueId}");
            }

            currentTarget = null;
            targetLostTime = 0f;
            ChangeState(MonsterAIState.Searching);
        }

        #endregion

        #region 移动控制

        /// <summary>
        /// 移动到目标位置
        /// </summary>
        private void MoveToTarget(Vector3 targetPosition)
        {
            if (owner?.entityObject == null) return;

            if (usePathfinding && pathfindingAgent != null)
            {
                // 使用PathfindingHelper设置目的地
                PathfindingHelper.SetDestination(pathfindingAgent, targetPosition);
                PathfindingHelper.ResumeMovement(pathfindingAgent);
                isMoving = true;

                // 检查是否到达目的地
                hasReachedDestination = PathfindingHelper.HasReachedDestination(pathfindingAgent, stopDistance);
            }
            else
            {
                // 简单的直线移动
                Vector3 direction = (targetPosition - owner.Position).normalized;
                Vector3 newPosition = owner.Position + direction * moveSpeed * Time.deltaTime;
                owner.Position = newPosition;

                // 旋转面向目标
                LookAtTarget(targetPosition);
                isMoving = true;
            }
        }

        /// <summary>
        /// 停止移动
        /// </summary>
        private void StopMovement()
        {
            if (usePathfinding && pathfindingAgent != null)
            {
                PathfindingHelper.StopMovement(pathfindingAgent);
            }
            isMoving = false;
        }

        /// <summary>
        /// 面向目标
        /// </summary>
        private void LookAtTarget(Vector3 targetPosition)
        {
            if (owner?.entityObject == null) return;

            Vector3 direction = (targetPosition - owner.Position).normalized;
            if (direction != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(direction);
                owner.entityObject.transform.rotation = Quaternion.RotateTowards(
                    owner.entityObject.transform.rotation,
                    targetRotation,
                    rotationSpeed * Time.deltaTime
                );
            }
        }

        #endregion

        #region 技能释放

        /// <summary>
        /// 更新技能释放逻辑
        /// </summary>
        private void UpdateSkillRelease(float deltaTime)
        {
            if (!enableAutoSkill || currentTarget == null || !currentTarget.IsAlive) return;

            // 检查是否在攻击状态
            if (stateMachine == null || !stateMachine.IsInState(MonsterAIStateMachine.AIState.Attacking)) return;

            // 检查技能释放间隔
            if (Time.time - lastSkillReleaseTime < skillReleaseInterval) return;

            // 检查距离
            float distanceToTarget = Vector3.Distance(owner.Position, currentTarget.Position);
            if (distanceToTarget > attackRange) return;

            // 尝试释放技能
            TryReleaseSkillAsync();
            lastSkillReleaseTime = Time.time;
        }

        /// <summary>
        /// 异步尝试释放技能
        /// </summary>
        private async void TryReleaseSkillAsync()
        {
            if (currentTarget == null) return;

            try
            {
                Vector3 direction = (currentTarget.Position - owner.Position).normalized;
                Vector3 targetPosition = currentTarget.Position;

                // 使用Entity的自动技能释放系统
                await owner.TryAutoReleaseSkills(direction, targetPosition);

                Debug.Log($"[MonsterAI {owner.UniqueId}] 向目标 {currentTarget.UniqueId} 释放技能");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[MonsterAI {owner.UniqueId}] 技能释放异常: {e.Message}");
            }
        }

        #endregion

        #region 状态管理（兼容性方法）

        /// <summary>
        /// 改变AI状态（兼容性方法）
        /// </summary>
        private void ChangeState(MonsterAIState newState)
        {
            // 转换为新的状态机状态
            var newStateMachineState = ConvertToStateMachineState(newState);
            stateMachine?.ChangeState(newStateMachineState);
        }

        /// <summary>
        /// 转换旧状态枚举到新状态机状态
        /// </summary>
        private MonsterAIStateMachine.AIState ConvertToStateMachineState(MonsterAIState oldState)
        {
            switch (oldState)
            {
                case MonsterAIState.Idle: return MonsterAIStateMachine.AIState.Idle;
                case MonsterAIState.Patrol: return MonsterAIStateMachine.AIState.Patrol;
                case MonsterAIState.Searching: return MonsterAIStateMachine.AIState.Searching;
                case MonsterAIState.Chasing: return MonsterAIStateMachine.AIState.Chasing;
                case MonsterAIState.Attacking: return MonsterAIStateMachine.AIState.Attacking;
                case MonsterAIState.Returning: return MonsterAIStateMachine.AIState.Returning;
                case MonsterAIState.Dead: return MonsterAIStateMachine.AIState.Dead;
                default: return MonsterAIStateMachine.AIState.Idle;
            }
        }

        /// <summary>
        /// 转换新状态机状态到旧状态枚举
        /// </summary>
        private MonsterAIState ConvertFromStateMachineState(MonsterAIStateMachine.AIState newState)
        {
            switch (newState)
            {
                case MonsterAIStateMachine.AIState.Idle: return MonsterAIState.Idle;
                case MonsterAIStateMachine.AIState.Patrol: return MonsterAIState.Patrol;
                case MonsterAIStateMachine.AIState.Searching: return MonsterAIState.Searching;
                case MonsterAIStateMachine.AIState.Chasing: return MonsterAIState.Chasing;
                case MonsterAIStateMachine.AIState.Attacking: return MonsterAIState.Attacking;
                case MonsterAIStateMachine.AIState.Returning: return MonsterAIState.Returning;
                case MonsterAIStateMachine.AIState.Stunned: return MonsterAIState.Idle; // 映射到空闲
                case MonsterAIStateMachine.AIState.Dead: return MonsterAIState.Dead;
                default: return MonsterAIState.Idle;
            }
        }

        #endregion

        #region 公共接口

        /// <summary>
        /// 获取当前AI状态
        /// </summary>
        public MonsterAIState GetCurrentState()
        {
            if (stateMachine == null) return MonsterAIState.Idle;
            return ConvertFromStateMachineState(stateMachine.GetCurrentState());
        }

        /// <summary>
        /// 获取当前状态机状态
        /// </summary>
        public MonsterAIStateMachine.AIState GetCurrentStateMachineState()
        {
            return stateMachine?.GetCurrentState() ?? MonsterAIStateMachine.AIState.Idle;
        }

        /// <summary>
        /// 获取当前目标
        /// </summary>
        public Entity GetCurrentTarget()
        {
            return currentTarget;
        }

        /// <summary>
        /// 强制设置目标
        /// </summary>
        public void ForceSetTarget(Entity target)
        {
            if (target != null && owner.IsValidTarget(target))
            {
                SetTarget(target);
                ChangeState(MonsterAIState.Chasing);
            }
        }

        /// <summary>
        /// 启用/禁用AI
        /// </summary>
        public void SetAIEnabled(bool enabled)
        {
            enableAI = enabled;
            if (!enabled)
            {
                StopMovement();
                currentTarget = null;
                stateMachine?.ChangeState(MonsterAIStateMachine.AIState.Idle);
            }
        }

        /// <summary>
        /// 设置眩晕状态
        /// </summary>
        public void SetStunned(float duration)
        {
            if (stateMachine != null)
            {
                stateMachine.ChangeState(MonsterAIStateMachine.AIState.Stunned);

                // 眩晕持续时间可以通过其他方式设置
                // 这里暂时使用默认持续时间
                Debug.Log($"[MonsterAI {owner.UniqueId}] 设置眩晕状态，持续时间: {duration}秒");
            }
        }

        /// <summary>
        /// 获取AI状态信息
        /// </summary>
        public string GetAIStatusInfo()
        {
            string targetInfo = currentTarget != null ? $"目标:{currentTarget.UniqueId}" : "无目标";
            string stateInfo = $"状态:{currentState}";
            string movingInfo = isMoving ? "移动中" : "静止";
            string pathfindingInfo = usePathfinding && pathfindingAgent != null ?
                PathfindingHelper.GetAgentStatusInfo(pathfindingAgent) : "无寻路";

            return $"[MonsterAI {owner.UniqueId}] {stateInfo}, {targetInfo}, {movingInfo}, {pathfindingInfo}";
        }

        #endregion
    }
}
